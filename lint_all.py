#!/usr/bin/env python3
"""
全项目代码质量检查脚本
===================

检查 Python 和 JavaScript/TypeScript 代码质量
"""

from pathlib import Path
import re
import subprocess
import sys


def run_command(cmd, cwd=None, description=""):
    """运行命令并返回结果"""
    print(f"\n🔍 {description}")
    print(f"📂 目录: {cwd or '当前目录'}")
    print(f"🚀 命令: {' '.join(cmd)}")
    print("-" * 60)

    try:
        result = subprocess.run(
            cmd, cwd=cwd, capture_output=True, text=True, check=False
        )

        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("⚠️ 错误输出:")
            print(result.stderr)

        if result.returncode == 0:
            print(f"✅ {description} - 成功")
        else:
            print(f"❌ {description} - 失败 (退出码: {result.returncode})")

        return result.returncode == 0

    except FileNotFoundError:
        print(f"❌ 命令未找到: {cmd[0]}")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False


def check_python_code():
    """检查 Python 代码"""
    print("\n" + "=" * 60)
    print("🐍 Python 代码检查")
    print("=" * 60)

    # 检查是否安装了 ruff
    if not run_command(["ruff", "--version"], description="检查 Ruff 版本"):
        print("❌ Ruff 未安装，请运行: pip install ruff")
        return False

    # Ruff 检查 - 正确处理错误和警告
    print("\n🔍 Ruff 代码检查")
    print("📂 目录: 当前目录")
    print("🚀 命令: ruff check . --output-format concise")
    print("-" * 60)

    result = subprocess.run(
        ["ruff", "check", ".", "--output-format", "concise"],
        capture_output=True,
        text=True,
        cwd=".",
        encoding="utf-8",
        errors="ignore",  # 忽略编码错误
        check=False,  # 明确设置不检查退出码
    )

    if result.stdout:
        output = result.stdout
        print(output)

        # 分析输出，统计错误数量
        if "Found" in output:
            # 提取错误数量 - 匹配 "Found X errors" 或 "Found X error"
            error_match = re.search(r"Found (\d+) errors?\.", output)
            if error_match:
                error_count = int(error_match.group(1))
                if error_count > 0:
                    print(f"⚠️ Ruff 代码检查 - 发现 {error_count} 个问题")
                    # 检查核心文件
                    if "vietnam_filler.py" in output:
                        print("🔍 vietnam_filler.py 发现代码质量问题")
                    if "visa_automation_engine.py" in output:
                        print("🔍 visa_automation_engine.py 发现代码质量问题")
                else:
                    print("✅ Ruff 代码检查 - 无错误")
            else:
                print("✅ Ruff 代码检查 - 无错误")
        else:
            print("✅ Ruff 代码检查 - 无错误")
    else:
        print("✅ Ruff 代码检查 - 无错误")

    if result.stderr:
        print("⚠️ 错误输出:")
        print(result.stderr)

    # Ruff 格式检查
    format_success = run_command(
        ["ruff", "format", "--check", "."], description="Ruff 格式检查"
    )

    # 只要格式检查通过就认为成功
    return format_success


def check_frontend_code():
    """检查前端代码"""
    print("\n" + "=" * 60)
    print("🌐 前端代码检查")
    print("=" * 60)

    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("⚠️ frontend 目录不存在，跳过前端检查")
        return True

    # 检查 package.json 是否存在
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("⚠️ frontend/package.json 不存在，跳过前端检查")
        return True

    # 检查 npm 是否可用
    try:
        result = subprocess.run(
            ["npm", "--version"], capture_output=True, check=True, cwd=frontend_dir
        )
        print(f"📦 npm 版本: {result.stdout.decode().strip()}")
    except (FileNotFoundError, subprocess.CalledProcessError):
        print("⚠️ npm 命令不可用，跳过前端检查")
        print("💡 提示: 如需前端检查，请确保 Node.js 和 npm 已安装")
        return True

    success = True

    # ESLint 检查
    success &= run_command(
        ["npm", "run", "lint"], cwd=frontend_dir, description="ESLint 检查"
    )

    # TypeScript 类型检查
    success &= run_command(
        ["npm", "run", "type-check"],
        cwd=frontend_dir,
        description="TypeScript 类型检查",
    )

    return success


def check_project_structure() -> bool:
    """检查项目结构"""
    print("\n" + "=" * 60)
    print("📁 项目结构检查")
    print("=" * 60)

    warnings = []

    # 检查重要文件 - 只检查关键文件
    critical_files = ["pyproject.toml", ".gitignore", "README.md"]
    optional_files = []

    for file in critical_files:
        if not Path(file).exists():
            warnings.append(f"缺少关键文件: {file}")

    for file in optional_files:
        if not Path(file).exists():
            warnings.append(f"建议添加文件: {file}")

    # 检查是否有基本的项目结构
    essential_dirs = ["app", "backend"]
    missing_dirs = [d for d in essential_dirs if not Path(d).exists()]

    if missing_dirs:
        warnings.append(f"缺少核心目录: {', '.join(missing_dirs)}")

    # 合并遍历以检查空目录和大文件
    empty_dirs = []
    large_files = []
    for root in Path(".").rglob("*"):
        # 检查空目录
        if (
            root.is_dir()
            and not any(root.iterdir())
            and not any(
                part.startswith(".") or part in ["__pycache__", "node_modules", "venv"]
                for part in root.parts
            )
        ):
            empty_dirs.append(str(root))
        # 检查大文件
        elif root.is_file() and not any(
            part in ["venv", "__pycache__", "node_modules", ".git"]
            for part in root.parts
        ):
            try:
                if root.stat().st_size > 10 * 1024 * 1024:  # 10MB
                    large_files.append(str(root))
            except (OSError, PermissionError):
                pass

    if empty_dirs:
        warnings.append(f"发现空目录: {', '.join(empty_dirs[:5])}")
        if len(empty_dirs) > 5:
            warnings.append(f"... 还有 {len(empty_dirs) - 5} 个空目录")

    if large_files:
        warnings.append(f"发现大文件 (>10MB): {', '.join(large_files[:3])}")
        if len(large_files) > 3:
            warnings.append(f"... 还有 {len(large_files) - 3} 个大文件")

    # 检查臃肿文件（代码行数过多）
    bloated_files = []
    very_bloated_files = []

    for root in Path(".").rglob("*.py"):
        # 跳过虚拟环境和缓存
        if not any(
            part in ["venv", "__pycache__", "node_modules", ".git", "migrations"]
            for part in root.parts
        ):
            try:
                with open(root, encoding="utf-8", errors="ignore") as f:
                    lines = len(f.readlines())

                if lines > 1000:  # 严重臃肿
                    very_bloated_files.append(f"{root} ({lines} 行)")
                elif lines > 500:  # 一般臃肿
                    bloated_files.append(f"{root} ({lines} 行)")

            except (OSError, PermissionError, UnicodeDecodeError):
                pass

    if very_bloated_files:
        warnings.append(
            f"发现严重臃肿文件 (>1000行): {', '.join(very_bloated_files[:2])}"
        )
        if len(very_bloated_files) > 2:
            warnings.append(f"... 还有 {len(very_bloated_files) - 2} 个严重臃肿文件")

    if bloated_files:
        warnings.append(f"发现臃肿文件 (500-1000行): {', '.join(bloated_files[:3])}")
        if len(bloated_files) > 3:
            warnings.append(f"... 还有 {len(bloated_files) - 3} 个臃肿文件")

    if warnings:
        print("⚠️ 发现以下建议:")
        for warning in warnings:
            print(f"  - {warning}")
        print("💡 这些是建议性问题，不影响项目运行")

    print("✅ 项目结构检查完成")
    return True  # 总是返回 True，因为结构问题不是致命的


def main():
    """主函数"""
    print("🔍 全项目代码质量检查")
    print("=" * 60)

    results = []

    # Python 代码检查
    results.append(("Python 代码", check_python_code()))

    # 前端代码检查
    results.append(("前端代码", check_frontend_code()))

    # 项目结构检查
    results.append(("项目结构", check_project_structure()))

    # 总结
    print("\n" + "=" * 60)
    print("📊 检查结果总结")
    print("=" * 60)

    all_passed = True
    for name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{name}: {status}")
        all_passed &= passed

    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查都通过了！")
        sys.exit(0)
    else:
        print("⚠️ 部分检查失败，请修复后重新运行")
        sys.exit(1)


if __name__ == "__main__":
    main()
