import type {
  ContactInfo,
  PassportInfo,
  PersonalInfo,
  PreviousVisitInfo,
  VietnamContactInfo,
  VisaInfo,
} from '@/types/form'
import { nextTick, watch } from 'vue'

// 🔥 表单持久化相关常量和类型
const FORM_STORAGE_KEY = 'visa_form_draft'
const FILES_DB_NAME = 'visa_form_files'
const FILES_DB_VERSION = 2
const FILES_STORE_NAME = 'uploaded_files'

interface SerializableFormData {
  personalInfo: PersonalInfo
  passportInfo: PassportInfo
  contactInfo: ContactInfo
  visaInfo: VisaInfo & PreviousVisitInfo & VietnamContactInfo
  filePreviews: {
    portrait_preview?: string
    passport_preview?: string
  }
  hasFiles: {
    portrait_photo: boolean
    passport_scan: boolean
  }
}

interface VisaFormStructure {
  personalInfo: PersonalInfo
  passportInfo: PassportInfo
  contactInfo: ContactInfo
  visaInfo: VisaInfo & PreviousVisitInfo & VietnamContactInfo
  files: {
    portrait_photo?: File
    passport_scan?: File
    portrait_preview?: string
    passport_preview?: string
  }
}

interface StoredFile {
  id: string
  file: File
  timestamp: number
}

export const useFormPersistence = () => {
  // 🔥 新增：IndexedDB操作函数
  const openFilesDB = (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(FILES_DB_NAME, FILES_DB_VERSION)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        if (!db.objectStoreNames.contains(FILES_STORE_NAME)) {
          const store = db.createObjectStore(FILES_STORE_NAME, { keyPath: 'id' })
          store.createIndex('timestamp', 'timestamp', { unique: false })
        }
      }
    })
  }

  const saveFileToIndexedDB = async (fileId: string, file: File): Promise<void> => {
    try {
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readwrite')
      const store = transaction.objectStore(FILES_STORE_NAME)

      const storedFile: StoredFile = {
        id: fileId,
        file: file,
        timestamp: Date.now(),
      }

      await new Promise((resolve, reject) => {
        const request = store.put(storedFile)
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })

      console.log(`💾 文件已保存到IndexedDB: ${fileId}`)
    } catch (error) {
      console.warn(`⚠️ 保存文件到IndexedDB失败: ${fileId}`, error)
    }
  }

  const loadFileFromIndexedDB = async (fileId: string): Promise<File | null> => {
    try {
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readonly')
      const store = transaction.objectStore(FILES_STORE_NAME)

      const result = await new Promise<StoredFile | null>((resolve, reject) => {
        const request = store.get(fileId)
        request.onsuccess = () => resolve(request.result ?? null)
        request.onerror = () => reject(request.error)
      })

      if (result) {
        console.log(`📄 从IndexedDB恢复文件: ${fileId}`)
        return result.file
      }
      return null
    } catch (error) {
      console.warn(`⚠️ 从IndexedDB加载文件失败: ${fileId}`, error)
      return null
    }
  }

  const deleteFileFromIndexedDB = async (fileId: string): Promise<void> => {
    try {
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readwrite')
      const store = transaction.objectStore(FILES_STORE_NAME)

      await new Promise((resolve, reject) => {
        const request = store.delete(fileId)
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })

      console.log(`🗑️ 已从IndexedDB删除文件: ${fileId}`)
    } catch (error) {
      console.warn(`⚠️ 从IndexedDB删除文件失败: ${fileId}`, error)
    }
  }

  const clearAllFilesFromIndexedDB = async (): Promise<void> => {
    try {
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readwrite')
      const store = transaction.objectStore(FILES_STORE_NAME)

      await new Promise((resolve, reject) => {
        const request = store.clear()
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })

      console.log('🗑️ 已清除IndexedDB中的所有文件')
    } catch (error) {
      console.warn('⚠️ 清除IndexedDB文件失败:', error)
    }
  }

  // 保存表单数据到本地存储（包括文件信息）
  const saveFormToStorage = async (formData: VisaFormStructure) => {
    try {
      // 保存文件到IndexedDB
      if (formData.files.portrait_photo) {
        await saveFileToIndexedDB('portrait_photo', formData.files.portrait_photo)
      }
      if (formData.files.passport_scan) {
        await saveFileToIndexedDB('passport_scan', formData.files.passport_scan)
      }

      // 保存可序列化的数据到localStorage
      const serializableData: SerializableFormData = {
        personalInfo: { ...formData.personalInfo },
        passportInfo: { ...formData.passportInfo },
        contactInfo: { ...formData.contactInfo },
        visaInfo: { ...formData.visaInfo },
        filePreviews: {
          portrait_preview: formData.files.portrait_preview,
          passport_preview: formData.files.passport_preview,
        },
        hasFiles: {
          portrait_photo: !!formData.files.portrait_photo,
          passport_scan: !!formData.files.passport_scan,
        },
      }

      localStorage.setItem(FORM_STORAGE_KEY, JSON.stringify(serializableData))
      console.log('💾 表单数据已保存到本地存储（包括文件）')
    } catch (error) {
      console.warn('⚠️ 保存表单数据到本地存储失败:', error)
    }
  }

  // 从本地存储加载表单数据
  const loadFormFromStorage = (): SerializableFormData | null => {
    try {
      const savedData = localStorage.getItem(FORM_STORAGE_KEY)
      if (savedData) {
        const parsed = JSON.parse(savedData) as SerializableFormData
        console.log('📄 从本地存储恢复表单数据')
        return parsed
      }
    } catch (error) {
      console.warn('⚠️ 从本地存储加载表单数据失败:', error)
    }
    return null
  }

  // 清除本地存储的表单数据（包括文件）
  const clearFormFromStorage = async () => {
    try {
      localStorage.removeItem(FORM_STORAGE_KEY)
      await clearAllFilesFromIndexedDB()
      console.log('🗑️ 已清除本地存储的表单数据（包括文件）')
    } catch (error) {
      console.warn('⚠️ 清除本地存储表单数据失败:', error)
    }
  }

  // 🔥 新增：清理孤立的文件索引（防止垃圾数据堆积）
  const cleanupOrphanedFileReferences = async (): Promise<number> => {
    try {
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readonly')
      const store = transaction.objectStore(FILES_STORE_NAME)

      const allFiles = await new Promise<StoredFile[]>((resolve, reject) => {
        const request = store.getAll()
        request.onsuccess = () => resolve(request.result || [])
        request.onerror = () => reject(request.error)
      })

      // 检查localStorage中的文件引用
      const formData = loadFormFromStorage()
      const referencedFiles = new Set<string>()

      if (formData?.hasFiles.portrait_photo) {
        referencedFiles.add('portrait_photo')
      }
      if (formData?.hasFiles.passport_scan) {
        referencedFiles.add('passport_scan')
      }

      // 找出孤立的文件（IndexedDB中存在但localStorage中没有引用）
      const orphanedFiles = allFiles.filter((file) => !referencedFiles.has(file.id))

      // 删除孤立文件
      let cleanedCount = 0
      const writeTransaction = db.transaction([FILES_STORE_NAME], 'readwrite')
      const writeStore = writeTransaction.objectStore(FILES_STORE_NAME)

      for (const orphanedFile of orphanedFiles) {
        await new Promise<void>((resolve, reject) => {
          const deleteRequest = writeStore.delete(orphanedFile.id)
          deleteRequest.onsuccess = () => {
            cleanedCount++
            resolve()
          }
          deleteRequest.onerror = () => reject(deleteRequest.error)
        })
      }

      if (cleanedCount > 0) {
        console.log(`🗑️ 清理孤立文件完成，删除 ${cleanedCount} 个孤立文件`)
      }

      return cleanedCount
    } catch (error) {
      console.warn('⚠️ 清理孤立文件失败:', error)
      return 0
    }
  }

  // 🔥 新增：获取存储使用统计
  const getStorageUsage = async (): Promise<{
    totalFiles: number
    totalSize: number
    formDataSize: number
    oldestFile: Date | null
  }> => {
    try {
      const db = await openFilesDB()
      const transaction = db.transaction([FILES_STORE_NAME], 'readonly')
      const store = transaction.objectStore(FILES_STORE_NAME)

      const allFiles = await new Promise<StoredFile[]>((resolve, reject) => {
        const request = store.getAll()
        request.onsuccess = () => resolve(request.result || [])
        request.onerror = () => reject(request.error)
      })

      let totalSize = 0
      let oldestFile: Date | null = null

      allFiles.forEach((file) => {
        totalSize += file.file?.size || 0
        const fileDate = new Date(file.timestamp)
        if (!oldestFile || fileDate < oldestFile) {
          oldestFile = fileDate
        }
      })

      // 计算localStorage中表单数据大小
      const formDataStr = localStorage.getItem(FORM_STORAGE_KEY) ?? ''
      const formDataSize = new Blob([formDataStr]).size

      return {
        totalFiles: allFiles.length,
        totalSize,
        formDataSize,
        oldestFile,
      }
    } catch (error) {
      console.warn('⚠️ 获取存储使用统计失败:', error)
      return {
        totalFiles: 0,
        totalSize: 0,
        formDataSize: 0,
        oldestFile: null,
      }
    }
  }

  // 检查表单数据是否为空
  const isFormDataEmpty = (formData: VisaFormStructure): boolean => {
    const personal = formData.personalInfo
    const passport = formData.passportInfo
    const contact = formData.contactInfo

    return (
      !personal.surname &&
      !personal.given_name &&
      !personal.chinese_name &&
      !personal.dob &&
      !personal.place_of_birth &&
      !passport.passport_number &&
      !passport.date_of_issue &&
      !passport.place_of_issue &&
      !passport.passport_expiry &&
      !contact.email &&
      !contact.telephone_number &&
      !formData.files.portrait_photo &&
      !formData.files.passport_scan
    )
  }

  // 初始化表单数据（从存储恢复，包括文件）
  const initializeFormData = async (
    formData: VisaFormStructure,
    updateValidationCallback?: () => void,
  ) => {
    const savedData = loadFormFromStorage()
    if (savedData) {
      // 恢复所有非文件数据
      Object.assign(formData.personalInfo, savedData.personalInfo)
      Object.assign(formData.passportInfo, savedData.passportInfo)
      Object.assign(formData.contactInfo, savedData.contactInfo)
      Object.assign(formData.visaInfo, savedData.visaInfo)

      // 恢复文件预览
      if (savedData.filePreviews.portrait_preview) {
        formData.files.portrait_preview = savedData.filePreviews.portrait_preview
      }
      if (savedData.filePreviews.passport_preview) {
        formData.files.passport_preview = savedData.filePreviews.passport_preview
      }

      // 🔥 新增：恢复文件对象
      if (savedData.hasFiles.portrait_photo) {
        const portraitFile = await loadFileFromIndexedDB('portrait_photo')
        if (portraitFile) {
          formData.files.portrait_photo = portraitFile
        }
      }
      if (savedData.hasFiles.passport_scan) {
        const passportFile = await loadFileFromIndexedDB('passport_scan')
        if (passportFile) {
          formData.files.passport_scan = passportFile
        }
      }

      // 延迟更新验证状态，确保数据完全恢复后再验证
      if (updateValidationCallback) {
        nextTick(() => {
          updateValidationCallback()
        })
      }

      return true
    }
    return false
  }

  // 设置自动保存监听
  const setupAutoSave = (formData: VisaFormStructure) => {
    let saveTimeout: number

    watch(
      () => formData,
      (newFormData) => {
        // 只在表单不为空时才保存，避免保存空表单
        if (!isFormDataEmpty(newFormData)) {
          // 使用防抖，避免频繁保存
          clearTimeout(saveTimeout)
          saveTimeout = setTimeout(() => {
            saveFormToStorage(newFormData)
          }, 1000) // 1秒后保存
        }
      },
      { deep: true },
    )

    return () => clearTimeout(saveTimeout) // 返回清理函数
  }

  return {
    saveFormToStorage,
    loadFormFromStorage,
    clearFormFromStorage,
    isFormDataEmpty,
    initializeFormData,
    setupAutoSave,

    // 🔥 新增：文件操作方法
    saveFileToIndexedDB,
    loadFileFromIndexedDB,
    deleteFileFromIndexedDB,
    clearAllFilesFromIndexedDB,

    // 🔥 新增：垃圾清理方法
    cleanupOrphanedFileReferences,
    getStorageUsage,
  }
}
