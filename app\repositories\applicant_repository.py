"""
申请人Repository - 申请人数据访问层
===============================

负责申请人相关的所有数据库操作，隔离业务逻辑与数据访问技术。
支持Session注入，符合依赖注入原则。
"""

from datetime import date
from typing import Any
from uuid import UUID

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.data.models.applicant import Applicant

from .base import SQLAlchemyRepository


class ApplicantRepository(SQLAlchemyRepository[Applicant, UUID]):
    """申请人Repository - 专门处理申请人数据访问"""

    def __init__(self, session: AsyncSession):
        """
        接受Session注入
        """
        super().__init__(session, Applicant)

    async def get_by_passport_number(self, passport_number: str) -> Applicant | None:
        """根据护照号获取申请人"""
        result = await self.session.execute(
            select(Applicant).where(Applicant.passport_number == passport_number)
        )
        return result.scalar_one_or_none()

    async def get_by_user_and_passport(
        self, user_id: UUID, passport_number: str
    ) -> Applicant | None:
        """根据用户ID和护照号获取申请人"""
        result = await self.session.execute(
            select(Applicant).where(
                and_(
                    Applicant.user_id == user_id,
                    Applicant.passport_number == passport_number,
                )
            )
        )
        return result.scalar_one_or_none()

    async def get_by_user_id(
        self, user_id: UUID, limit: int = 20, offset: int = 0
    ) -> list[Applicant]:
        """获取用户的申请人列表"""
        result = await self.session.execute(
            select(Applicant)
            .where(Applicant.user_id == user_id)
            .order_by(desc(Applicant.created_at))
            .limit(limit)
            .offset(offset)
        )
        return result.scalars().all()

    async def count_by_user_id(self, user_id: UUID) -> int:
        """统计用户申请人数量"""
        result = await self.session.execute(
            select(func.count(Applicant.id)).where(Applicant.user_id == user_id)
        )
        return result.scalar()

    async def search_applicants(
        self,
        user_id: UUID | None = None,
        surname: str | None = None,
        given_name: str | None = None,
        chinese_name: str | None = None,
        passport_number: str | None = None,
        nationality: str | None = None,
        limit: int = 50,
        offset: int = 0,
    ) -> list[Applicant]:
        """搜索申请人"""
        query = select(Applicant)

        conditions = []

        if user_id:
            conditions.append(Applicant.user_id == user_id)

        if surname:
            conditions.append(Applicant.surname.ilike(f"%{surname}%"))

        if given_name:
            conditions.append(Applicant.given_name.ilike(f"%{given_name}%"))

        if chinese_name:
            conditions.append(Applicant.chinese_name.ilike(f"%{chinese_name}%"))

        if passport_number:
            conditions.append(Applicant.passport_number.ilike(f"%{passport_number}%"))

        if nationality:
            conditions.append(Applicant.nationality == nationality)

        if conditions:
            query = query.where(and_(*conditions))

        query = query.order_by(desc(Applicant.created_at)).limit(limit).offset(offset)

        result = await self.session.execute(query)
        return result.scalars().all()

    async def get_applicants_by_birth_date_range(
        self,
        start_date: date,
        end_date: date,
        user_id: UUID | None = None,
        limit: int = 100,
    ) -> list[Applicant]:
        """根据出生日期范围获取申请人"""
        query = select(Applicant).where(
            and_(
                Applicant.date_of_birth >= start_date,
                Applicant.date_of_birth <= end_date,
            )
        )

        if user_id:
            query = query.where(Applicant.user_id == user_id)

        query = query.order_by(Applicant.date_of_birth).limit(limit)

        result = await self.session.execute(query)
        return result.scalars().all()

    async def get_applicants_by_passport_expiry_range(
        self,
        start_date: date,
        end_date: date,
        user_id: UUID | None = None,
        limit: int = 100,
    ) -> list[Applicant]:
        """根据护照到期日期范围获取申请人"""
        query = select(Applicant).where(
            and_(
                Applicant.date_of_expiry >= start_date,
                Applicant.date_of_expiry <= end_date,
            )
        )

        if user_id:
            query = query.where(Applicant.user_id == user_id)

        query = query.order_by(Applicant.date_of_expiry).limit(limit)

        result = await self.session.execute(query)
        return result.scalars().all()

    async def check_duplicate_applicant(
        self,
        user_id: UUID,
        passport_number: str,
        surname: str,
        given_name: str,
        date_of_birth: date,
    ) -> Applicant | None:
        """检查重复申请人（基于关键信息）"""
        result = await self.session.execute(
            select(Applicant).where(
                and_(
                    Applicant.user_id == user_id,
                    Applicant.passport_number == passport_number,
                    Applicant.surname == surname,
                    Applicant.given_name == given_name,
                    Applicant.date_of_birth == date_of_birth,
                )
            )
        )
        return result.scalar_one_or_none()

    async def get_applicant_statistics(
        self, user_id: UUID | None = None
    ) -> dict[str, Any]:
        """获取申请人统计信息"""
        query = select(Applicant)

        if user_id:
            query = query.where(Applicant.user_id == user_id)

        # 总数统计
        total_result = await self.session.execute(
            select(func.count(Applicant.id)).select_from(query.subquery())
        )
        total_count = total_result.scalar()

        # 按国籍统计
        nationality_result = await self.session.execute(
            select(Applicant.nationality, func.count(Applicant.id))
            .select_from(query.subquery())
            .group_by(Applicant.nationality)
        )
        nationality_stats = dict(nationality_result.fetchall())

        # 按性别统计
        gender_result = await self.session.execute(
            select(Applicant.sex, func.count(Applicant.id))
            .select_from(query.subquery())
            .group_by(Applicant.sex)
        )
        gender_stats = dict(gender_result.fetchall())

        return {
            "total_count": total_count,
            "nationality_distribution": nationality_stats,
            "gender_distribution": gender_stats,
        }

    async def find_or_create_applicant(
        self, user_id: UUID, applicant_data: dict[str, Any]
    ) -> tuple[Applicant, bool]:
        """查找或创建申请人，返回(申请人, 是否新创建)"""
        # 首先尝试查找现有申请人
        existing = await self.check_duplicate_applicant(
            user_id=user_id,
            passport_number=applicant_data["passport_number"],
            surname=applicant_data["surname"],
            given_name=applicant_data["given_name"],
            date_of_birth=applicant_data["date_of_birth"],
        )

        if existing:
            return existing, False

        # 创建新申请人
        new_applicant = Applicant(user_id=user_id, **applicant_data)

        created_applicant = await self.create(new_applicant)
        return created_applicant, True
