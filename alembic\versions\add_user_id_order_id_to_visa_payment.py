"""添加user_id和order_id到visa_payment表以完善双层支付架构

Revision ID: add_visa_payment_fields
Revises: add_user_id_fields
Create Date: 2025-06-22 15:30:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "add_visa_payment_fields"
down_revision = "add_user_id_fields"
branch_labels = None
depends_on = None


def upgrade():
    """
    🔐 完善双层支付架构：为visa_payment表添加user_id和order_id字段

    目标：
    1. 简化visa_payment表的权限查询
    2. 增强双层支付架构的数据一致性
    3. 支持直接的用户和订单关联
    4. 提升查询性能
    """
    print("\n🔐 开始完善双层支付架构...")
    print("=" * 60)

    # 第一阶段：添加user_id字段到visa_payment表
    print("\n💳 第一阶段：为visa_payment表添加user_id字段")
    print("-" * 50)

    print("  🔧 添加user_id字段...")
    op.add_column(
        "visa_payment",
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=True),
    )

    print("  📊 填充visa_payment.user_id数据...")
    # 通过application和order表填充user_id
    op.execute("""
        UPDATE visa_payment
        SET user_id = o.user_id
        FROM application a
        JOIN "order" o ON a.order_id = o.id
        WHERE visa_payment.application_id = a.id
    """)

    print("  🔒 设置visa_payment.user_id为NOT NULL...")
    op.alter_column("visa_payment", "user_id", nullable=False)

    print("  🔗 添加visa_payment.user_id外键约束...")
    op.create_foreign_key(
        "visa_payment_user_id_fkey",
        "visa_payment",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )

    print("  📊 创建visa_payment.user_id索引...")
    op.create_index("ix_visa_payment_user_id", "visa_payment", ["user_id"])

    # 第二阶段：添加order_id字段到visa_payment表
    print("\n💳 第二阶段：为visa_payment表添加order_id字段")
    print("-" * 50)

    print("  🔧 添加order_id字段...")
    op.add_column(
        "visa_payment",
        sa.Column("order_id", postgresql.UUID(as_uuid=True), nullable=True),
    )

    print("  📊 填充visa_payment.order_id数据...")
    # 通过application表填充order_id
    op.execute("""
        UPDATE visa_payment
        SET order_id = a.order_id
        FROM application a
        WHERE visa_payment.application_id = a.id
    """)

    print("  🔒 设置visa_payment.order_id为NOT NULL...")
    op.alter_column("visa_payment", "order_id", nullable=False)

    print("  🔗 添加visa_payment.order_id外键约束...")
    op.create_foreign_key(
        "visa_payment_order_id_fkey",
        "visa_payment",
        "order",
        ["order_id"],
        ["id"],
        ondelete="CASCADE",
    )

    print("  📊 创建visa_payment.order_id索引...")
    op.create_index("ix_visa_payment_order_id", "visa_payment", ["order_id"])

    # 第三阶段：创建复合索引优化查询性能
    print("\n💳 第三阶段：创建性能优化索引")
    print("-" * 50)

    print("  🎯 创建visa_payment复合索引...")
    op.create_index(
        "ix_visa_payment_user_status", "visa_payment", ["user_id", "status"]
    )
    op.create_index(
        "ix_visa_payment_user_created", "visa_payment", ["user_id", "created_at"]
    )
    op.create_index(
        "ix_visa_payment_order_app", "visa_payment", ["order_id", "application_id"]
    )
    op.create_index(
        "ix_visa_payment_app_status", "visa_payment", ["application_id", "status"]
    )

    # 验证修复结果
    print("\n🔍 验证双层支付架构修复结果...")
    print("-" * 50)

    # 检查数据完整性
    connection = op.get_bind()

    # 验证visa_payment表user_id
    user_id_count = connection.execute(
        sa.text("SELECT COUNT(*) FROM visa_payment WHERE user_id IS NULL")
    ).scalar()

    if user_id_count == 0:
        print("  ✅ visa_payment表user_id数据完整")
    else:
        print(f"  ❌ visa_payment表有{user_id_count}条记录缺少user_id")

    # 验证visa_payment表order_id
    order_id_count = connection.execute(
        sa.text("SELECT COUNT(*) FROM visa_payment WHERE order_id IS NULL")
    ).scalar()

    if order_id_count == 0:
        print("  ✅ visa_payment表order_id数据完整")
    else:
        print(f"  ❌ visa_payment表有{order_id_count}条记录缺少order_id")

    # 验证数据一致性
    consistency_check = connection.execute(
        sa.text("""
            SELECT COUNT(*) FROM visa_payment v
            JOIN application a ON v.application_id = a.id
            JOIN "order" o ON a.order_id = o.id
            WHERE v.user_id != o.user_id OR v.order_id != o.id
        """)
    ).scalar()

    if consistency_check == 0:
        print("  ✅ visa_payment表数据一致性验证通过")
    else:
        print(f"  ❌ 发现{consistency_check}条数据不一致记录")

    print("\n🎉 双层支付架构完善完成！")
    print("📋 修复摘要：")
    print("  ✅ visa_payment表添加user_id字段和索引")
    print("  ✅ visa_payment表添加order_id字段和索引")
    print("  ✅ 创建外键约束确保数据一致性")
    print("  ✅ 创建复合索引优化查询性能")
    print("  🔐 现在支持双层支付架构的直接权限控制")
    print("\n💡 双层支付架构说明：")
    print("  📊 USER_PAYMENT: 用户向平台付费（订单级别，CNY）")
    print("  💳 VISA_PAYMENT: 平台代付签证费（申请级别，USD）")
    print("  🔗 两层都支持直接的用户权限控制")


def downgrade():
    """
    回滚双层支付架构修复
    """
    print("\n🔄 回滚双层支付架构修复...")

    # 删除复合索引
    op.drop_index("ix_visa_payment_app_status", "visa_payment")
    op.drop_index("ix_visa_payment_order_app", "visa_payment")
    op.drop_index("ix_visa_payment_user_created", "visa_payment")
    op.drop_index("ix_visa_payment_user_status", "visa_payment")

    # 删除单字段索引
    op.drop_index("ix_visa_payment_order_id", "visa_payment")
    op.drop_index("ix_visa_payment_user_id", "visa_payment")

    # 删除外键约束
    op.drop_constraint("visa_payment_order_id_fkey", "visa_payment", type_="foreignkey")
    op.drop_constraint("visa_payment_user_id_fkey", "visa_payment", type_="foreignkey")

    # 删除字段
    op.drop_column("visa_payment", "order_id")
    op.drop_column("visa_payment", "user_id")

    print("  ✅ 双层支付架构修复已回滚")
