"""add display_name and auth fields to user table

彻底移除orders、order_sequence、order_status_history等历史表相关迁移，仅保留user表相关迁移内容。

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "4a278b4fa3d9"
down_revision: Union[str, None] = "dea71b346875"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user", sa.Column("display_name", sa.String(length=64), nullable=True)
    )
    op.add_column("user", sa.Column("phone", sa.String(length=32), nullable=True))
    op.add_column(
        "user", sa.Column("hashed_password", sa.String(length=128), nullable=True)
    )
    op.add_column("user", sa.Column("is_superuser", sa.<PERSON>(), nullable=True))
    op.add_column("user", sa.<PERSON>umn("is_verified", sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "is_verified")
    op.drop_column("user", "is_superuser")
    op.drop_column("user", "hashed_password")
    op.drop_column("user", "phone")
    op.drop_column("user", "display_name")
    # 此迁移文件已不再包含任何历史表相关迁移，仅保留user表相关内容。
    # ### end Alembic commands ###
