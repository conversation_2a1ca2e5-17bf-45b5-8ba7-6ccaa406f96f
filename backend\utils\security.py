# api/utils/security.py
"""
安全工具模块
"""

import base64
import hashlib
import re
import secrets
from typing import Any

from cryptography.fernet import Fernet


def mask_sensitive_data(data: str, show_chars: int = 4, mask_char: str = "*") -> str:
    """
    对敏感数据进行掩码处理

    Args:
        data: 需要掩码的敏感数据
        show_chars: 显示的字符数量
        mask_char: 掩码字符

    Returns:
        掩码后的字符串
    """
    if not data:
        return ""

    if len(data) <= show_chars:
        return mask_char * len(data)

    return mask_char * (len(data) - show_chars) + data[-show_chars:]


def mask_email(email: str) -> str:
    """
    对邮箱地址进行掩码处理

    Args:
        email: 邮箱地址

    Returns:
        掩码后的邮箱地址
    """
    if not email or "@" not in email:
        return mask_sensitive_data(email)

    local, domain = email.split("@", 1)

    if len(local) <= 2:
        masked_local = "*" * len(local)
    else:
        masked_local = local[0] + "*" * (len(local) - 2) + local[-1]

    return f"{masked_local}@{domain}"


def mask_credit_card(card_number: str) -> str:
    """
    对信用卡号进行掩码处理

    Args:
        card_number: 信用卡号

    Returns:
        掩码后的信用卡号
    """
    # 移除所有非数字字符
    digits_only = re.sub(r"\D", "", card_number)

    if len(digits_only) < 4:
        return "*" * len(digits_only)

    # 保留最后4位
    masked = "*" * (len(digits_only) - 4) + digits_only[-4:]

    # 每4位添加空格
    chunks = [masked[i : i + 4] for i in range(0, len(masked), 4)]
    return " ".join(chunks)


def mask_passport_number(passport: str) -> str:
    """
    对护照号进行掩码处理

    Args:
        passport: 护照号

    Returns:
        掩码后的护照号
    """
    return mask_sensitive_data(passport, show_chars=3)


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除危险字符

    Args:
        filename: 原始文件名

    Returns:
        清理后的文件名
    """
    # 移除路径遍历字符
    filename = filename.replace("..", "").replace("/", "").replace("\\", "")

    # 只保留字母、数字、点、下划线、连字符
    filename = re.sub(r"[^a-zA-Z0-9._-]", "", filename)

    # 限制长度
    if len(filename) > 255:
        name, ext = filename.rsplit(".", 1) if "." in filename else (filename, "")
        filename = name[:250] + ("." + ext if ext else "")

    return filename


def generate_secure_token(length: int = 32) -> str:
    """
    生成安全的随机令牌

    Args:
        length: 令牌长度

    Returns:
        安全令牌
    """
    return secrets.token_urlsafe(length)


def hash_data(data: str, salt: str | None = None) -> str:
    """
    对数据进行哈希处理

    Args:
        data: 要哈希的数据
        salt: 盐值

    Returns:
        哈希值
    """
    if salt is None:
        salt = secrets.token_hex(16)

    hash_obj = hashlib.sha256()
    hash_obj.update((data + salt).encode("utf-8"))
    return hash_obj.hexdigest()


def mask_log_data(data: dict[str, Any]) -> dict[str, Any]:
    """
    对日志数据进行脱敏处理

    Args:
        data: 原始数据字典

    Returns:
        脱敏后的数据字典
    """
    sensitive_fields = {
        "password",
        "passwd",
        "pwd",
        "secret",
        "token",
        "key",
        "credit_card",
        "card_number",
        "cvv",
        "ssn",
        "passport",
        "email",
        "phone",
        "mobile",
        "telephone",
    }

    masked_data = {}

    for key, value in data.items():
        key_lower = key.lower()

        if any(field in key_lower for field in sensitive_fields):
            if isinstance(value, str):
                if "email" in key_lower:
                    masked_data[key] = mask_email(value)
                elif "card" in key_lower or "credit" in key_lower:
                    masked_data[key] = mask_credit_card(value)
                elif "passport" in key_lower:
                    masked_data[key] = mask_passport_number(value)
                else:
                    masked_data[key] = mask_sensitive_data(value)
            else:
                masked_data[key] = "***MASKED***"
        else:
            masked_data[key] = value

    return masked_data


def validate_input_length(value: str, max_length: int, field_name: str) -> str:
    """
    验证输入长度

    Args:
        value: 输入值
        max_length: 最大长度
        field_name: 字段名

    Returns:
        验证后的值

    Raises:
        ValueError: 如果长度超限
    """
    if len(value) > max_length:
        raise ValueError(f"{field_name} 长度不能超过 {max_length} 字符")
    return value


def validate_no_sql_injection(value: str, field_name: str) -> str:
    """
    简单的SQL注入检测

    Args:
        value: 输入值
        field_name: 字段名

    Returns:
        验证后的值

    Raises:
        ValueError: 如果检测到可疑内容
    """
    # 简单的SQL注入关键词检测
    sql_keywords = [
        "select",
        "insert",
        "update",
        "delete",
        "drop",
        "create",
        "alter",
        "exec",
        "execute",
        "union",
        "script",
        "javascript",
    ]

    value_lower = value.lower()
    for keyword in sql_keywords:
        if keyword in value_lower:
            raise ValueError(f"{field_name} 包含不允许的内容")

    return value


def validate_no_xss(value: str, field_name: str) -> str:
    """
    简单的XSS检测

    Args:
        value: 输入值
        field_name: 字段名

    Returns:
        验证后的值

    Raises:
        ValueError: 如果检测到可疑内容
    """
    # 检测HTML标签和JavaScript
    xss_patterns = [
        r"<script.*?>.*?</script>",  # script tags
        r"<.*?on\w+\s*=.*?>",  # inline event handlers
        r"javascript:",  # javascript: pseudo-protocol
        r"vbscript:",  # vbscript: pseudo-protocol
        r"<iframe.*?>",  # iframe tags
        r"<object.*?>",  # object tags
        r"<embed.*?>",  # embed tags
        r"data:text/html.*base64",  # base64-encoded HTML
        r"src\s*=\s*[\"']?javascript:",  # src attribute with javascript
        r"style\s*=\s*[\"']?expression\(",  # CSS expression
        r"<img.*?src=.*?javascript:.*?>",  # img tag with javascript src
        r"<link.*?href=.*?javascript:.*?>",  # link tag with javascript href
        r"<meta.*?http-equiv\s*=\s*[\"']?refresh.*?>",  # meta refresh
        r"&#x.{2,};",  # HTML entity encoding
        r"&\#\d{2,};",  # numeric HTML entities
        r"<svg.*?>.*?</svg>",  # svg tags
        r"<math.*?>.*?</math>",  # math tags
        r"<form.*?>.*?</form>",  # form tags
    ]

    for pattern in xss_patterns:
        if re.search(pattern, value, re.IGNORECASE):
            raise ValueError(f"{field_name} 包含不允许的内容")

    return value


class SimpleEncryption:
    """简单的加密工具类"""

    def __init__(self, key: str | None = None):
        if key is None:
            key = Fernet.generate_key()
        elif isinstance(key, str):
            key = base64.urlsafe_b64encode(key.ljust(32)[:32].encode())

        self.cipher = Fernet(key)

    def encrypt(self, data: str) -> str:
        """加密数据"""
        return self.cipher.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
