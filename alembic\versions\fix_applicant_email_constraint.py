"""fix applicant email constraint

Revision ID: fix_applicant_email_constraint
Revises: optimize_database_performance
Create Date: 2025-06-20 16:00:00.000000

"""

from alembic import op
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = "fix_applicant_email_constraint"
down_revision = "optimize_database_performance"
branch_labels = None
depends_on = None


def upgrade():
    """
    修复applicant表邮箱唯一约束问题

    问题：uq_applicant_email约束阻止同一邮箱多次申请签证
    解决：删除该约束，允许同一邮箱多次申请
    """
    print("🔧 开始修复applicant邮箱约束问题...")

    # 获取数据库连接
    connection = op.get_bind()

    # 检查约束是否存在
    constraint_exists = connection.execute(
        text("""
        SELECT COUNT(*) FROM information_schema.table_constraints
        WHERE table_name = 'applicant'
        AND constraint_type = 'UNIQUE'
        AND constraint_name = 'uq_applicant_email'
    """)
    ).scalar()

    if constraint_exists > 0:
        print("  🗑️ 删除 uq_applicant_email 唯一约束...")
        op.drop_constraint("uq_applicant_email", "applicant", type_="unique")
        print("  ✅ 成功删除 uq_applicant_email 约束")
    else:
        print("  ✅ uq_applicant_email 约束不存在，无需删除")

    print("✅ applicant邮箱约束修复完成")
    print("📝 现在同一邮箱可以多次申请签证")


def downgrade():
    """
    回滚：重新创建邮箱唯一约束
    注意：如果数据库中已有重复邮箱，此操作会失败
    """
    print("🔄 回滚：重新创建applicant邮箱唯一约束...")

    # 检查是否有重复邮箱
    connection = op.get_bind()
    duplicate_count = connection.execute(
        text("""
        SELECT COUNT(*) FROM (
            SELECT email, COUNT(*) as cnt
            FROM applicant
            WHERE email IS NOT NULL
            GROUP BY email
            HAVING COUNT(*) > 1
        ) duplicates
    """)
    ).scalar()

    if duplicate_count > 0:
        print(f"  ⚠️ 发现 {duplicate_count} 个重复邮箱，无法创建唯一约束")
        print("  💡 请先清理重复数据或选择不回滚此迁移")
        return

    op.create_unique_constraint("uq_applicant_email", "applicant", ["email"])
    print("  ✅ uq_applicant_email 约束已重新创建")
