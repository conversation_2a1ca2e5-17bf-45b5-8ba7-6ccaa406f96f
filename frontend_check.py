#!/usr/bin/env python3
"""
前端代码质量检查
================

专门用于检查 Vue 3 + TypeScript 前端项目的代码质量
包括 ESLint、TypeScript 类型检查、Prettier 格式检查等
"""

from pathlib import Path
import subprocess
import sys


def run_command(cmd, cwd=None, description=""):
    """运行命令并返回结果"""
    try:
        print(f"\n🔍 {description}")
        print(f"📂 目录: {cwd or '当前目录'}")
        print(f"🚀 命令: {' '.join(cmd)}")
        print("-" * 60)

        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=120,  # 2分钟超时
            shell=True,  # Windows 需要 shell=True
        )

        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr)

        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            return True
        else:
            print(f"❌ {description} - 失败 (退出码: {result.returncode})")
            return False

    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False
    except FileNotFoundError:
        print(f"❌ 命令未找到: {cmd[0]}")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False


def check_frontend_environment():
    """检查前端开发环境"""
    print("🌐 前端环境检查")
    print("=" * 50)

    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend 目录不存在")
        return False, "no_frontend_dir"

    # 检查 package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ package.json 不存在")
        return False, "no_package_json"

    print("✅ 前端项目结构存在")

    # 检查 Node.js 和 npm
    has_node = False
    has_npm = False

    try:
        result = subprocess.run(
            ["node", "--version"],
            capture_output=True,
            text=True,
            shell=True,  # Windows 需要 shell=True
        )
        if result.returncode == 0:
            print(f"✅ Node.js 版本: {result.stdout.strip()}")
            has_node = True
        else:
            print("❌ Node.js 命令执行失败")
    except FileNotFoundError:
        print("❌ Node.js 未安装")

    try:
        result = subprocess.run(
            ["npm", "--version"],
            capture_output=True,
            text=True,
            shell=True,  # Windows 需要 shell=True
        )
        if result.returncode == 0:
            print(f"✅ npm 版本: {result.stdout.strip()}")
            has_npm = True
        else:
            print("❌ npm 命令执行失败")
    except FileNotFoundError:
        print("❌ npm 未安装")

    if not has_node or not has_npm:
        print("\n💡 前端检查需要 Node.js 环境:")
        print("   1. 下载安装 Node.js: https://nodejs.org/")
        print("   2. 安装依赖: cd frontend && npm install")
        print("   3. 重新运行检查: python frontend_check.py")
        return False, "no_nodejs"

    # 检查 node_modules
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("⚠️ node_modules 不存在，请运行: npm install")
        return False, "no_node_modules"

    print("✅ 前端环境检查通过")
    return True, "ok"


def check_eslint():
    """ESLint 代码检查"""
    frontend_dir = Path("frontend")

    # 检查是否有 ESLint 配置
    eslint_configs = [
        "eslint.config.ts",
        "eslint.config.js",
        ".eslintrc.js",
        ".eslintrc.json",
    ]

    has_config = any((frontend_dir / config).exists() for config in eslint_configs)
    if not has_config:
        print("⚠️ 未找到 ESLint 配置文件")
        return True

    # 自定义 ESLint 检查，正确处理警告
    print("\n🔍 ESLint 代码检查")
    print(f"📂 目录: {frontend_dir}")
    print("🚀 命令: npm run lint")
    print("-" * 60)

    try:
        result = subprocess.run(
            ["npm", "run", "lint"],
            cwd=frontend_dir,
            capture_output=True,
            text=True,
            shell=True,
            encoding="utf-8",
            errors="ignore",  # 忽略编码错误
        )

        if result.stdout:
            output = result.stdout
            print(output)

            # 分析输出
            if "✖" in output and "problems" in output:
                # 提取问题数量
                import re

                match = re.search(
                    r"✖ (\d+) problems \((\d+) errors, (\d+) warnings\)", output
                )
                if match:
                    _, errors, warnings = match.groups()
                    if int(errors) > 0:
                        print(
                            f"❌ ESLint 检查 - 发现 {errors} 个错误，{warnings} 个警告"
                        )
                        return False
                    elif int(warnings) > 0:
                        print(f"⚠️ ESLint 检查 - 发现 {warnings} 个警告（可接受）")
                        return True
            else:
                print("✅ ESLint 检查 - 无问题")
                return True

        if result.stderr:
            print(result.stderr)

        return result.returncode == 0

    except Exception as e:
        print(f"❌ ESLint 检查失败: {e}")
        return False


def check_typescript():
    """TypeScript 类型检查"""
    frontend_dir = Path("frontend")

    # 检查是否有 TypeScript 配置
    ts_configs = ["tsconfig.json", "tsconfig.app.json"]
    has_config = any((frontend_dir / config).exists() for config in ts_configs)

    if not has_config:
        print("⚠️ 未找到 TypeScript 配置文件")
        return True

    return run_command(
        ["npm", "run", "type-check"],
        cwd=frontend_dir,
        description="TypeScript 类型检查",
    )


def check_prettier():
    """Prettier 格式检查"""
    frontend_dir = Path("frontend")

    # 检查是否有 Prettier 配置或在 package.json 中配置
    prettier_configs = [
        ".prettierrc",
        ".prettierrc.json",
        ".prettierrc.js",
        "prettier.config.js",
    ]

    has_config = any((frontend_dir / config).exists() for config in prettier_configs)

    # 检查 package.json 中是否有 format 脚本
    try:
        import json

        with open(frontend_dir / "package.json", encoding="utf-8") as f:
            package_data = json.load(f)
            has_format_script = "format" in package_data.get("scripts", {})
    except (FileNotFoundError, json.JSONDecodeError, KeyError, OSError):
        has_format_script = False

    if not has_config and not has_format_script:
        print("⚠️ 未找到 Prettier 配置")
        return True

    if has_format_script:
        # 使用 --check 参数检查格式，不实际修改文件
        return run_command(
            ["npx", "prettier", "--check", "src/"],
            cwd=frontend_dir,
            description="Prettier 格式检查",
        )

    return True


def main():
    """主函数"""
    print("🚀 前端代码质量检查")
    print("=" * 60)

    # 环境检查
    env_ok, env_status = check_frontend_environment()
    if not env_ok:
        if env_status == "no_nodejs":
            print("\n⚠️ 需要 Node.js 环境才能进行前端代码检查")
            print("💡 这是正常的，如果您只进行后端开发")
        else:
            print(f"\n❌ 前端环境检查失败: {env_status}")
        sys.exit(1)

    # 代码质量检查
    results = []

    results.append(("ESLint 检查", check_eslint()))
    results.append(("TypeScript 检查", check_typescript()))
    results.append(("Prettier 检查", check_prettier()))

    # 总结
    print("\n" + "=" * 60)
    print("📊 前端检查结果总结")
    print("=" * 60)

    all_passed = True
    for name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{name}: {status}")
        all_passed &= passed

    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 前端代码质量检查通过！")
        print("💡 提示: 定期运行检查以保持代码质量")
        sys.exit(0)
    else:
        print("⚠️ 前端代码质量检查发现问题")
        print("🔧 建议:")
        print("   1. 运行 npm run lint 修复 ESLint 问题")
        print("   2. 运行 npm run type-check 检查类型错误")
        print("   3. 运行 npm run format 修复格式问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
