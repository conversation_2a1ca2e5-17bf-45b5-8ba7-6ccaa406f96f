"""add missing company fields to user table

Revision ID: add_missing_company_fields
Revises: 4a278b4fa3d9
Create Date: 2025-06-17 07:20:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "add_missing_company_fields"
down_revision: Union[str, None] = "4a278b4fa3d9"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    只添加3个缺失的company字段，与User模型完全一致：
    - company_name: VARCHAR(128), nullable=True
    - company_address: VARCHAR(256), nullable=True
    - company_contacts: VARCHAR(64), nullable=True
    """
    # 添加company_name字段
    op.add_column(
        "user", sa.Column("company_name", sa.String(length=128), nullable=True)
    )

    # 添加company_address字段
    op.add_column(
        "user", sa.Column("company_address", sa.String(length=256), nullable=True)
    )

    # 添加company_contacts字段
    op.add_column(
        "user", sa.Column("company_contacts", sa.String(length=64), nullable=True)
    )


def downgrade() -> None:
    """
    回滚操作：删除添加的3个字段
    """
    op.drop_column("user", "company_contacts")
    op.drop_column("user", "company_address")
    op.drop_column("user", "company_name")
