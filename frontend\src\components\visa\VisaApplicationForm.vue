<template>
  <div class="visa-details-panel">
    <div class="visa-info-form">
      <div class="form-content">
        <!-- 第一行：签证类型、签证有效期、出行目的 -->
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="签证类型" required>
              <el-radio-group
                v-model="visaInfo.visa_entry_type"
                @change="validateVisaInfo"
                class="custom-radio-group"
              >
                <el-radio value="Single-entry">单次</el-radio>
                <el-radio value="Multiple-entry">多次</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="签证有效期" required>
              <el-radio-group
                v-model="visaInfo.visa_validity_duration"
                @change="validateVisaInfo"
                class="custom-radio-group"
              >
                <el-radio value="30天">30天</el-radio>
                <el-radio value="90天">90天</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="出行目的" required>
              <el-select
                v-model="visaInfo.purpose_of_entry"
                placeholder="选择出行目的"
                @change="validateVisaInfo"
                class="full-width-select"
              >
                <el-option value="Tourist" label="旅游" />
                <el-option value="Business" label="商务" />
                <el-option value="Visiting relatives" label="探亲" />
                <el-option value="Working" label="工作" />
                <el-option value="Other" label="其他" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：签证生效日期、入境口岸、近1年是否去过越南 -->
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="签证生效日期" required>
              <el-date-picker
                v-model="visaInfo.visa_start_date"
                type="date"
                placeholder="DD/MM/YYYY"
                format="DD/MM/YYYY"
                value-format="DD/MM/YYYY"
                :disabled-date="disabledDate"
                @change="handleDateChange"
                class="full-width-date"
              />
              <!-- 出签生效选项放在签证生效日期下方 -->
              <div class="expedited-options">
                <el-radio-group
                  v-model="visaInfo.expedited_type"
                  @change="handleExpeditedChange"
                  class="expedited-group custom-radio-group"
                >
                  <el-radio value="4days">4工出签</el-radio>
                  <el-radio value="3days">3工加急</el-radio>
                  <el-radio value="2days">2工加急</el-radio>
                  <el-radio value="1days">1工加急</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="入境口岸" required>
              <el-select
                v-model="visaInfo.intended_entry_gate"
                placeholder="选择入境口岸"
                @change="validateVisaInfo"
                class="full-width-select"
              >
                <el-option value="Tan Son Nhat Int Airport (Ho Chi Minh City)" label="胡志明" />
                <el-option value="Noi Bai Int Airport" label="河内" />
                <el-option value="Da Nang International Airport" label="岘港" />
                <el-option value="Cam Ranh Int Airport (Khanh Hoa)" label="芽庄" />
                <el-option value="Mong Cai Landport" label="东兴" />
                <el-option value="Huu Nghi Landport" label="友谊" />
              </el-select>
            </el-form-item>

            <el-form-item label="越南联系组织/个人">
              <el-radio-group
                v-model="visaInfo.has_vietnam_contact"
                @change="handleVietnamContactChange"
                class="custom-radio-group"
              >
                <el-radio :value="false">无</el-radio>
                <el-radio :value="true">有</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="近1年是否去过越南" required>
              <el-radio-group
                v-model="visaInfo.visited_vietnam_last_year"
                @change="handleVisitedChange"
                class="custom-radio-group"
              >
                <el-radio :value="false">否</el-radio>
                <el-radio :value="true">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 上次访问详细信息 -->
        <div v-if="visaInfo.visited_vietnam_last_year" class="previous-visit-details">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="之前访问日期">
                <el-date-picker
                  v-model="visaInfo.previous_entry_date"
                  type="date"
                  placeholder="DD/MM/YYYY"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  class="full-width-date"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="之前离开日期">
                <el-date-picker
                  v-model="visaInfo.previous_exit_date"
                  type="date"
                  placeholder="DD/MM/YYYY"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  class="full-width-date"
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="之前访问目的">
                <el-input
                  v-model="visaInfo.previous_purpose"
                  placeholder="Tourism"
                  class="full-width-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 越南联系组织详细信息 -->
        <div v-if="visaInfo.has_vietnam_contact" class="vietnam-contact-details">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="组织名称">
                <el-input
                  v-model="visaInfo.vietnam_contact_organization"
                  placeholder="请输入联系组织名称"
                  class="full-width-input"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="联系电话">
                <el-input
                  v-model="visaInfo.vietnam_contact_phone"
                  placeholder="请输入联系电话"
                  class="full-width-input"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="联系地址">
                <el-input
                  v-model="visaInfo.vietnam_contact_address"
                  placeholder="请输入联系地址"
                  class="full-width-input"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="联系目的">
                <el-input
                  v-model="visaInfo.vietnam_contact_purpose"
                  placeholder="请输入目的"
                  class="full-width-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * VisaApplicationForm - 签证申请表单组件
 *
 * 专门负责处理签证申请相关信息的表单输入，包括：
 * - 签证类型选择（单次/多次入境）
 * - 签证有效期选择（30天/90天）
 * - 出行目的选择（旅游/商务/探亲/工作/其他）
 * - 签证生效日期设置
 * - 加急类型选择（1工/2工/3工/4工）
 * - 入境口岸选择
 * - 近1年越南访问历史
 * - 上次访问详细信息（条件显示）
 *
 * @component
 * @example
 * ```vue
 * <VisaApplicationForm
 *   v-model="visaInfo"
 *   :get-field-class="getFieldClass"
 *   @validate-visa="handleVisaValidation"
 *   @expedited-type-change="handleExpeditedTypeChange"
 * />
 * ```
 */

import { onMounted } from 'vue'
import { useFormValidation } from '@/composables/useFormValidation'
import type { VisaInfo, PreviousVisitInfo, VietnamContactInfo } from '@/types/form'

/**
 * 组件事件接口
 */
interface Emits {
  /** 签证信息验证结果事件 */
  (e: 'validate-visa', isValid: boolean): void
  /** 加急类型变更事件 */
  (e: 'expedited-type-change', value: string): void
}

const emit = defineEmits<Emits>()

// v-model 绑定签证信息
const visaInfo = defineModel<VisaInfo & PreviousVisitInfo & VietnamContactInfo>({ required: true })

// 使用验证 composable
const {
  disabledDate,
  validateVisaInfo: validateVisaInfoCore,
  handleDateChange: handleDateChangeCore,
  handleExpeditedChange: handleExpeditedChangeCore,
  handleVisitedChange: handleVisitedChangeCore,
  handleVietnamContactChange: handleVietnamContactChangeCore,
  initializeValidation,
} = useFormValidation()

/**
 * 验证签证信息完整性
 *
 * 包装 useFormValidation 的验证方法以适配组件接口
 */
const validateVisaInfo = (): void => {
  const result = validateVisaInfoCore(visaInfo.value)
  emit('validate-visa', result.isValid)
}

/**
 * 处理日期输入框变化事件
 *
 * @param value - 用户选择的日期值
 */
const handleDateChange = (value: string): void => {
  handleDateChangeCore(visaInfo.value, value)
  validateVisaInfo()
}

/**
 * 处理加急类型变化
 *
 * @param value - 选择的加急类型（'1days' | '2days' | '3days' | '4days'）
 */
const handleExpeditedChange = async (value: string): Promise<void> => {
  await handleExpeditedChangeCore(visaInfo.value, value)
  emit('expedited-type-change', value)
  validateVisaInfo()
}

/**
 * 处理近期访问变化
 *
 * 当用户改选"否"（未访问过越南）时，清空相关的历史访问字段
 */
const handleVisitedChange = (): void => {
  handleVisitedChangeCore(visaInfo.value)
  validateVisaInfo()
}

/**
 * 处理越南联系组织变化
 *
 * 当用户改选"否"（无联系组织）时，清空相关的联系组织字段
 */
const handleVietnamContactChange = (): void => {
  handleVietnamContactChangeCore(visaInfo.value)
  validateVisaInfo()
}

// 初始化验证
onMounted(async () => {
  await initializeValidation()
})
</script>

<style scoped lang="scss">
.visa-details-panel {
  flex: 1;
  padding-left: 24px;

  @media (max-width: 768px) {
    padding-left: 0;
    padding-top: 24px;

    // 移动端三列变为单列
    .visa-info-form .form-content {
      :deep(.el-row) {
        .el-col {
          &[class*='span-8'] {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
      }
    }
  }

  .visa-info-form {
    .form-content {
      // 水平单选按钮组布局 - 使用 el-radio-button
      .expedited-options {
        margin-top: 8px;

        .expedited-group {
          display: flex;
          gap: 8px; // 调整间距从20px到8px
        }
      }

      // 自定义单选按钮样式
      .custom-radio-group {
        display: flex;
        gap: 16px;

        :deep(.el-radio) {
          margin-right: 0;

          .el-radio__input {
            &.is-checked {
              .el-radio__inner {
                width: 18px;
                height: 18px;
                background-color: var(--el-color-primary);
                border-color: var(--el-color-primary);

                &::after {
                  content: '✓';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%) rotate(15deg);
                  font-size: 12px;
                  font-weight: bold;
                  color: white;
                  line-height: 1;
                  width: auto;
                  height: auto;
                  border-radius: 0;
                  background: transparent;
                }
              }
            }
          }

          .el-radio__inner {
            width: 18px;
            height: 18px;
            border: 2px solid var(--el-border-color);
            border-radius: 50%;
            background-color: white;
            transition: all 0.3s ease;

            &::after {
              // 隐藏默认的实心圆点
              background: transparent;
              border: none;
            }

            &:hover {
              border-color: var(--el-color-primary);
              background-color: rgba(64, 158, 255, 0.1);
              transform: scale(1.05);
            }
          }

          &:hover {
            .el-radio__inner {
              border-color: var(--el-color-primary);
              background-color: rgba(64, 158, 255, 0.1);
              transform: scale(1.05);
            }

            .el-radio__label {
              color: var(--el-color-primary);
            }
          }

          &.is-checked {
            .el-radio__inner {
              &:hover {
                transform: scale(1.05);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
              }
            }

            .el-radio__label {
              color: var(--el-color-primary);
              font-weight: 600;
            }
          }

          .el-radio__label {
            font-size: 14px;
            color: var(--el-text-color-regular);
            padding-left: 10px;
            font-weight: 500;
          }
        }
      }

      .previous-visit-details {
        margin-top: 16px;
        padding-top: 16px;
      }

      .vietnam-contact-details {
        margin-top: 16px;
        padding-top: 16px;
      }

      // 宽度控制类
      .half-width-select,
      .half-width-input {
        width: 50%;
      }

      .full-width-date,
      .full-width-select {
        width: 100%;
      }
    }
  }
}
</style>
