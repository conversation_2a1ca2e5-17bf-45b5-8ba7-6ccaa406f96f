"""
环境变量加载模块 - 用于安全地加载环境变量和配置
"""

import os
from pathlib import Path
from typing import Any

from app.utils.logger_config import get_logger

logger = get_logger()

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent


def load_env_var(key: str, default: str | None = None) -> str | None:
    """
    从环境变量加载配置值

    Args:
        key: 环境变量名称
        default: 默认值，如果环境变量不存在

    Returns:
        环境变量的值或默认值
    """
    value = os.environ.get(key, default)
    # 记录日志，但不显示实际值（避免泄露敏感信息）
    if value is not None:
        if (
            "PASSWORD" in key
            or "KEY" in key
            or "SECRET" in key
            or "CARD" in key
            or "CVV" in key
        ):
            logger.debug(f"已加载环境变量: {key}=******")
        else:
            logger.debug(f"已加载环境变量: {key}={value}")
    else:
        logger.debug(f"环境变量未设置: {key}")
    return value


def get_aliyun_appcode() -> str | None:
    """
    获取阿里云APPCODE

    Returns:
        Optional[str]: APPCODE或None
    """
    return load_env_var("ALIYUN_APPCODE")


def get_anti_captcha_key() -> str | None:
    """
    获取Anti-Captcha API密钥

    Returns:
        Optional[str]: API密钥或None
    """
    return load_env_var("ANTI_CAPTCHA_API_KEY")


def load_email_accounts_from_env() -> dict[str, Any]:
    """
    从环境变量加载邮箱账户配置

    Returns:
        Dict[str, Any]: 包含email_accounts和allowed_senders的字典
    """
    email_accounts = {}
    allowed_senders = {}

    # 查找所有EMAIL_USER_开头的环境变量
    i = 1
    while True:
        user_key = f"EMAIL_USER_{i}"
        if user_key not in os.environ:
            break

        email = os.environ[user_key]
        host = os.environ.get(f"EMAIL_HOST_{i}")
        port = os.environ.get(f"EMAIL_PORT_{i}")
        password = os.environ.get(f"EMAIL_PASSWORD_{i}")

        if not all([email, host, port, password]):
            logger.warning(f"邮箱配置 #{i} 不完整，跳过")
            i += 1
            continue

        # 解析允许的发件人列表
        senders_str = os.environ.get(f"EMAIL_ALLOWED_SENDERS_{i}", "")
        senders = [s.strip() for s in senders_str.split(",") if s.strip()]

        # 创建邮箱配置
        email_accounts[email] = {
            "host": host,
            "port": int(port),
            "user": email,
            "password": password,
        }

        # 设置允许的发件人
        allowed_senders[email] = senders

        logger.info(f"从环境变量加载邮箱配置: {email}")
        i += 1

    return {"email_accounts": email_accounts, "allowed_senders": allowed_senders}


def load_credit_cards_from_env() -> list[dict[str, Any]]:
    """
    从环境变量加载信用卡信息，严格匹配payment_cards.json的格式和字段顺序

    Returns:
        List[Dict[str, Any]]: 信用卡信息列表
    """
    credit_cards = []

    # 查找所有CREDIT_CARD_开头的环境变量
    i = 1
    while True:
        # 按照JSON文件中的确切顺序定义字段
        type_key = f"CREDIT_CARD_{i}_TYPE"
        number_key = f"CREDIT_CARD_{i}_NUMBER"

        # 如果没有找到卡号键，则退出循环
        if number_key not in os.environ:
            logger.warning(f"信用卡配置 #{i} 缺少卡号，跳过")
            break

        # 获取所有信用卡字段，严格按照JSON文件中的顺序
        card_type = os.environ.get(type_key, "")
        card_number = os.environ.get(number_key, "")
        first_name = os.environ.get(f"CREDIT_CARD_{i}_FIRST_NAME", "")
        last_name = os.environ.get(f"CREDIT_CARD_{i}_LAST_NAME", "")
        billing_address = os.environ.get(f"CREDIT_CARD_{i}_BILLING_ADDRESS", "")
        city = os.environ.get(f"CREDIT_CARD_{i}_CITY", "")
        country = os.environ.get(f"CREDIT_CARD_{i}_COUNTRY", "")
        exp_month = os.environ.get(f"CREDIT_CARD_{i}_EXP_MONTH", "")
        exp_year = os.environ.get(f"CREDIT_CARD_{i}_EXP_YEAR", "")
        cvv = os.environ.get(f"CREDIT_CARD_{i}_CVV", "")
        note = os.environ.get(f"CREDIT_CARD_{i}_NOTE", "")

        # 检查必要字段是否存在
        if not card_number:
            logger.warning(f"信用卡配置 #{i} 缺少卡号，跳过")
            i += 1
            continue

        # 创建信用卡配置，严格匹配payment_cards.json的格式和字段顺序
        credit_card = {
            "card_type": card_type,
            "card_number": card_number,
            "first_name": first_name,
            "last_name": last_name,
            "billing_address": billing_address,
            "city": city,
            "country": country,
            "exp_month": exp_month,
            "exp_year": exp_year,
            "cvv": cvv,
            "note": note,
        }

        credit_cards.append(credit_card)
        logger.info(
            f"从环境变量加载信用卡配置: {card_type} ****{card_number[-4:] if len(card_number) >= 4 else '????'}"
        )
        i += 1

    return credit_cards


def load_browser_config_from_env() -> dict[str, Any]:
    """
    从环境变量加载浏览器配置

    Returns:
        Dict[str, Any]: 浏览器配置
    """
    browser_config = {
        "browser_type": load_env_var("BROWSER", "chromium"),
        "headless": load_env_var("HEADLESS", "true").lower() == "true",
        "slow_mo": int(load_env_var("SLOW_MO", "0")),
        "viewport_width": int(load_env_var("VIEWPORT_WIDTH", "1920")),
        "viewport_height": int(load_env_var("VIEWPORT_HEIGHT", "1080")),
        "page_zoom": float(load_env_var("PAGE_ZOOM", "0.8")),
        "timeout_ms": int(load_env_var("TIMEOUT_MS", "300000")),
    }

    logger.info(
        f"从环境变量加载浏览器配置: 类型={browser_config['browser_type']}, 无头模式={browser_config['headless']}"
    )
    return browser_config


def load_config_with_env(env_mappings: dict[str, str] = None) -> dict[str, Any]:
    """
    仅从环境变量加载配置，不使用配置文件

    Args:
        config_path: 配置文件路径（仅用于日志记录，不再读取）
        env_mappings: 配置键到环境变量名的映射

    Returns:
        Dict[str, Any]: 从环境变量加载的配置
    """
    config = {}

    logger.info("仅从环境变量加载配置")

    # 从环境变量加载配置
    if env_mappings:
        for config_key, env_var in env_mappings.items():
            env_value = os.environ.get(env_var)
            if env_value is not None:
                # 处理嵌套键，如 "database.host"
                keys = config_key.split(".")
                if len(keys) == 1:
                    config[keys[0]] = env_value
                else:
                    current = config
                    for key in keys[:-1]:
                        if key not in current:
                            current[key] = {}
                        current = current[key]
                    current[keys[-1]] = env_value

                # 记录日志，但不显示敏感信息
                if (
                    "password" in config_key.lower()
                    or "key" in config_key.lower()
                    or "secret" in config_key.lower()
                    or "card" in config_key.lower()
                ):
                    logger.debug(f"从环境变量加载配置: {config_key}=******")
                else:
                    logger.debug(f"从环境变量加载配置: {config_key}={env_value}")

    return config
