<template>
  <div class="dashboard-view">
    <!-- 页面标题和刷新信息 -->
    <div class="page-header">
      <div class="header-info">
        <span>数据总览</span>
        <span class="update-time">最后更新: {{ lastUpdateTime }}</span>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-section">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.basicStats.value.total_applications }}</div>
              <div class="stat-label">今日申请数</div>
              <div class="stat-change info">实时: {{ realtimeApplicationCount }} 个</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.basicStats.value.pending_applications }}</div>
              <div class="stat-label">申请提交中</div>
              <div class="stat-change warning">
                实时: {{ realtimeStats.pendingApplications }} 个
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon success">
              <el-icon><Select /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">
                {{ statistics.basicStats.value.successful_applications }}
              </div>
              <div class="stat-label">成功申请</div>
              <div class="stat-change positive">成功率: {{ successRate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.basicStats.value.total_applications }}</div>
              <div class="stat-label">总申请数</div>
              <div class="stat-change info">实时: {{ realtimeApplicationCount }} 个</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表控制区域 -->
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="chart-controls">
              <span class="title">数据分析</span>
              <div class="control-actions">
                <el-select
                  v-model="statistics.currentPeriod"
                  placeholder="选择时间周期"
                  style="width: 120px; margin-right: 12px"
                  @change="handlePeriodChange"
                >
                  <el-option label="最近24小时" value="1d" />
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
                <el-button
                  type="primary"
                  size="small"
                  @click="handleRefresh"
                  :loading="statistics.loading"
                >
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>

          <el-row :gutter="24">
            <!-- 申请趋势图 -->
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <TimeSeriesChart
                :dates="statistics.chartData.value.timeSeries.dates"
                :applications="statistics.chartData.value.timeSeries.applications"
                :successful="statistics.chartData.value.timeSeries.successful"
                :loading="statistics.loading.value"
                title="申请趋势图"
              />
            </el-col>

            <!-- 状态分布饼图 -->
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <StatusDistributionChart
                :data="statistics.chartData.value.statusPie"
                :loading="statistics.loading.value"
                title="状态分布"
              />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计信息 -->
    <el-row :gutter="24" style="margin-top: 24px">
      <!-- 支付统计 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <el-card class="detail-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><CreditCard /></el-icon>
              <span>支付统计</span>
            </div>
          </template>

          <div class="payment-stats">
            <div class="stat-row">
              <span class="label">已支付:</span>
              <span class="value success">{{ statistics.paymentStats.value.paid_count }}</span>
            </div>
            <div class="stat-row">
              <span class="label">待支付:</span>
              <span class="value warning">{{ statistics.paymentStats.value.pending_count }}</span>
            </div>
            <div class="stat-row">
              <span class="label">支付失败:</span>
              <span class="value danger">{{ statistics.paymentStats.value.failed_count }}</span>
            </div>
            <div class="stat-row total">
              <span class="label">总金额:</span>
              <span class="value"
                >¥{{ statistics.paymentStats.value.total_amount.toLocaleString() }}</span
              >
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 处理时间统计 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <el-card class="detail-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Timer /></el-icon>
              <span>处理时间统计</span>
            </div>
          </template>

          <div class="processing-stats">
            <div class="stat-row">
              <span class="label">平均处理时间:</span>
              <span class="value"
                >{{ statistics.processingTimeStats.value.average_hours }}小时</span
              >
            </div>
            <div class="stat-row">
              <span class="label">中位数:</span>
              <span class="value">{{ statistics.processingTimeStats.value.median_hours }}小时</span>
            </div>
            <div class="stat-row">
              <span class="label">最短时间:</span>
              <span class="value">{{ statistics.processingTimeStats.value.min_hours }}小时</span>
            </div>
            <div class="stat-row">
              <span class="label">最长时间:</span>
              <span class="value">{{ statistics.processingTimeStats.value.max_hours }}小时</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Calendar,
  Clock,
  Select,
  DataAnalysis,
  Refresh,
  CreditCard,
  Timer,
} from '@element-plus/icons-vue'
import { useStatistics } from '@/composables/useStatistics'
import { statisticsApi } from '@/api/statistics'
import TimeSeriesChart from '@/components/statistics/TimeSeriesChart.vue'
import StatusDistributionChart from '@/components/statistics/StatusDistributionChart.vue'

// 集成统计数据
const statistics = useStatistics({
  autoRefresh: true,
  refreshInterval: 60000, // 1分钟刷新一次
  defaultPeriod: '7d',
})

// 页面状态
const lastUpdateTime = ref(new Date().toLocaleTimeString())

// 实时统计数据
const realtimeStatsData = ref({
  todayApplications: 0,
  pendingApplications: 0,
  successApplications: 0,
  failedApplications: 0,
  totalApplications: 0,
})

// 实时统计计算属性
const realtimeApplicationCount = computed(() => {
  return realtimeStatsData.value.totalApplications
})

const realtimeStats = computed(() => {
  return realtimeStatsData.value
})

// 获取实时统计数据
const fetchRealtimeStats = async () => {
  try {
    const response = await statisticsApi.getRealtimeStats()
    if (response.success && response.data) {
      realtimeStatsData.value = {
        todayApplications: response.data.today_applications || 0,
        pendingApplications: response.data.pending_applications || 0,
        successApplications: response.data.successful_applications || 0,
        failedApplications: response.data.failed_applications || 0,
        totalApplications: response.data.total_applications || 0,
      }
      console.log('✅ 实时统计数据更新成功:', realtimeStatsData.value)
    }
  } catch (error) {
    console.error('❌ 获取实时统计数据失败:', error)
  }
}

const successRate = computed(() => {
  const total =
    statistics.basicStats.value.successful_applications +
    statistics.basicStats.value.failed_applications
  if (total === 0) return 0
  return Math.round((statistics.basicStats.value.successful_applications / total) * 100)
})

// 事件处理
const handleRefresh = () => {
  statistics.refresh()
  fetchRealtimeStats() // 同时刷新实时统计
  lastUpdateTime.value = new Date().toLocaleTimeString()
}

const handlePeriodChange = (period: string) => {
  statistics.changePeriod(period)
}

// 组件挂载时初始化
onMounted(() => {
  fetchRealtimeStats() // 初始加载实时统计

  // 设置定期刷新实时统计（每30秒）
  const realtimeStatsInterval = setInterval(fetchRealtimeStats, 30000)

  // 在组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(realtimeStatsInterval)
  })
})

// 组件卸载时清理
onUnmounted(() => {
  statistics.cleanup()
})
</script>

<style scoped lang="scss">
.dashboard-view {
  width: 100%;
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    .header-info {
      display: flex;
      align-items: center;
      gap: 12px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #67c23a;
      border-radius: 8px;
      padding: 12px 16px;
      max-width: max-content;

      span {
        color: #2d3748;
        font-weight: 500;
        font-size: 14px;
      }

      .update-time {
        font-size: 12px;
        color: #718096;
        font-weight: 400;
        margin-left: 8px;
      }
    }
  }

  .stat-card {
    .stat-item {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
          font-size: 24px;
          color: white;
        }

        &.today {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.pending {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.success {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.total {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: var(--el-text-color-primary);
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          margin-bottom: 4px;
        }

        .stat-change {
          font-size: 12px;
          font-weight: 500;

          &.positive {
            color: #67c23a;
          }

          &.warning {
            color: #e6a23c;
          }

          &.info {
            color: #409eff;
          }
        }
      }
    }
  }

  .chart-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .control-actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .detail-card {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .payment-stats,
    .processing-stats {
      .stat-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }

        &.total {
          margin-top: 8px;
          padding-top: 16px;
          border-top: 2px solid var(--el-color-primary);
          font-weight: 600;
        }

        .label {
          color: var(--el-text-color-regular);
          font-size: 14px;
        }

        .value {
          font-size: 16px;
          font-weight: 600;

          &.success {
            color: #67c23a;
          }

          &.warning {
            color: #e6a23c;
          }

          &.danger {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-view {
    padding: 15px;

    .page-header {
      margin-bottom: 16px;

      .header-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .stat-card {
      margin-bottom: 16px;

      .stat-item {
        .stat-icon {
          width: 50px;
          height: 50px;

          .el-icon {
            font-size: 20px;
          }
        }

        .stat-content {
          .stat-number {
            font-size: 24px;
          }

          .stat-label {
            font-size: 13px;
          }

          .stat-change {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
