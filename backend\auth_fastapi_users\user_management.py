"""
FastAPI Users 用户管理脚本
用于创建admin用户和管理用户账户
"""

import asyncio
import uuid

from sqlalchemy import select

from backend.auth_fastapi_users.database import get_async_session
from backend.auth_fastapi_users.manager import get_user_manager
from backend.auth_fastapi_users.models import User


async def create_user_interactive(
    username: str, email: str, password: str, is_superuser: bool = False
) -> User | None:
    """
    交互式创建用户

    Args:
        username: 用户名
        email: 邮箱地址
        password: 密码
        is_superuser: 是否为管理员

    Returns:
        创建的用户对象或None
    """
    async with get_async_session() as session:
        # 检查邮箱是否已存在
        stmt = select(User).where(User.email == email)
        result = await session.execute(stmt)
        existing_user = result.scalar_one_or_none()

        if existing_user:
            print(f"⚠️ 邮箱 '{email}' 已存在")
            return None

        # 检查用户名是否已存在
        stmt = select(User).where(User.username == username)
        result = await session.execute(stmt)
        existing_user = result.scalar_one_or_none()

        if existing_user:
            print(f"⚠️ 用户名 '{username}' 已存在")
            return None

        # 创建用户管理器 - 使用统一连接池适配器
        from backend.auth_fastapi_users.database import get_user_db

        async for user_db in get_user_db():
            async for user_manager in get_user_manager(user_db):
                try:
                    # 创建用户对象
                    user_create = {
                        "id": uuid.uuid4(),
                        "username": username,
                        "email": email,
                        "is_active": True,
                        "is_superuser": is_superuser,
                        "is_verified": True,
                        "role": "admin" if is_superuser else "user",
                    }

                    # 使用用户管理器创建用户
                    user = User(**user_create)
                    # 加密密码
                    user.hashed_password = user_manager.password_helper.hash(password)

                    # 添加到数据库
                    session.add(user)
                    await session.commit()
                    await session.refresh(user)

                    return user

                except Exception as e:
                    print(f"❌ 创建用户失败: {e}")
                    await session.rollback()
                    return None


async def get_user_by_email(email: str) -> User | None:
    """根据邮箱获取用户"""
    async with get_async_session() as session:
        stmt = select(User).where(User.email == email)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


async def change_user_password(email: str, new_password: str) -> bool:
    """修改用户密码"""
    async with get_async_session() as session:
        # 获取用户
        stmt = select(User).where(User.email == email)
        result = await session.execute(stmt)
        user = result.scalar_one_or_none()

        if not user:
            return False

        # 创建用户管理器来加密密码 - 使用统一连接池适配器
        from backend.auth_fastapi_users.database import get_user_db

        async for user_db in get_user_db():
            async for user_manager in get_user_manager(user_db):
                try:
                    # 加密新密码
                    user.hashed_password = user_manager.password_helper.hash(
                        new_password
                    )

                    # 保存到数据库
                    session.add(user)
                    await session.commit()

                    return True

                except Exception as e:
                    print(f"❌ 修改密码失败: {e}")
                    await session.rollback()
                    return False


async def delete_user_by_email(email: str) -> bool:
    """根据邮箱删除用户"""
    async with get_async_session() as session:
        # 获取用户
        stmt = select(User).where(User.email == email)
        result = await session.execute(stmt)
        user = result.scalar_one_or_none()

        if not user:
            return False

        try:
            await session.delete(user)
            await session.commit()
            return True
        except Exception as e:
            print(f"❌ 删除用户失败: {e}")
            await session.rollback()
            return False


async def toggle_user_admin(email: str, make_admin: bool) -> bool:
    """设置/取消用户管理员权限"""
    async with get_async_session() as session:
        # 获取用户
        stmt = select(User).where(User.email == email)
        result = await session.execute(stmt)
        user = result.scalar_one_or_none()

        if not user:
            return False

        try:
            user.is_superuser = make_admin
            user.role = "admin" if make_admin else "user"

            session.add(user)
            await session.commit()
            return True
        except Exception as e:
            print(f"❌ 修改权限失败: {e}")
            await session.rollback()
            return False


async def create_admin_user(
    username: str, password: str, email: str = None
) -> User | None:
    """
    创建管理员用户

    Args:
        username: 用户名
        password: 密码
        email: 邮箱（可选，默认为********************）

    Returns:
        创建的用户对象或None
    """
    if email is None:
        email = f"{username}@admin.local"

    return await create_user_interactive(
        username=username, email=email, password=password, is_superuser=True
    )


async def create_users_from_config():
    """
    从config/users.json创建用户
    """
    import json
    from pathlib import Path

    # 读取用户配置
    config_path = Path("config/users.json")
    if not config_path.exists():
        print("❌ config/users.json 文件不存在")
        return

    try:
        with open(config_path, encoding="utf-8") as f:
            users_config = json.load(f)

        print(f"📋 发现 {len(users_config)} 个用户配置")

        for username, user_data in users_config.items():
            password = user_data.get("password")
            role = user_data.get("role", "user")

            if not password:
                print(f"⚠️ 跳过用户 '{username}': 缺少密码")
                continue

            # 创建用户
            user = await create_admin_user(
                username=username,
                password=password,
                email=f"{username}@visa-automator.local",
            )

            if user and role != "admin":
                # 如果不是admin角色，更新权限
                async with get_async_session() as session:
                    user.is_superuser = False
                    user.role = role
                    session.add(user)
                    await session.commit()
                    print(f"   更新用户权限: role='{role}', is_superuser=False")

    except Exception as e:
        print(f"❌ 读取用户配置失败: {e}")


async def list_users():
    """列出所有用户"""
    async with get_async_session() as session:
        stmt = select(User)
        result = await session.execute(stmt)
        users = result.scalars().all()

        print(f"📋 系统中的用户 ({len(users)} 个):")
        for user in users:
            print(f"   ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   邮箱: {user.email}")
            print(f"   角色: {user.role}")
            print(f"   激活: {user.is_active}")
            print(f"   管理员: {user.is_superuser}")
            print(f"   已验证: {user.is_verified}")
            print("   ---")


if __name__ == "__main__":
    import sys

    async def main():
        if len(sys.argv) > 1 and sys.argv[1] == "create_from_config":
            await create_users_from_config()
        elif len(sys.argv) > 1 and sys.argv[1] == "list":
            await list_users()
        else:
            # 默认创建admin用户
            await create_admin_user("admin", "1C#\\r4@%kL}8Zhy)]#")

    asyncio.run(main())
