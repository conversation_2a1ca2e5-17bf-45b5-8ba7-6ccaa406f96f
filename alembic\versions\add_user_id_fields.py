"""添加user_id到application和visa_status_history表以增强权限控制

Revision ID: add_user_id_fields
Revises: optimize_database_performance
Create Date: 2025-06-22 14:50:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "add_user_id_fields"
down_revision = "4a676de393d7"  # 使用当前的head
branch_labels = None
depends_on = None


def upgrade():
    """
    🔐 架构安全修复：添加user_id字段到application和visa_status_history表

    目标：
    1. 简化权限查询逻辑
    2. 提升查询性能
    3. 增强数据一致性
    4. 减少JOIN查询复杂度
    """
    print("\n🔐 开始数据库架构安全修复...")
    print("=" * 60)

    # 第一阶段：添加user_id字段到application表
    print("\n📋 第一阶段：修复application表")
    print("-" * 40)

    print("  🔧 添加user_id字段到application表...")
    op.add_column(
        "application",
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=True),
    )

    print("  📊 填充application.user_id数据...")
    # 通过order表填充user_id
    op.execute("""
        UPDATE application
        SET user_id = o.user_id
        FROM "order" o
        WHERE application.order_id = o.id
    """)

    print("  🔒 设置application.user_id为NOT NULL...")
    op.alter_column("application", "user_id", nullable=False)

    print("  🔗 添加application.user_id外键约束...")
    op.create_foreign_key(
        "application_user_id_fkey",
        "application",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )

    print("  📊 创建application.user_id索引...")
    op.create_index("ix_application_user_id", "application", ["user_id"])

    # 第二阶段：添加user_id字段到visa_status_history表
    print("\n📋 第二阶段：修复visa_status_history表")
    print("-" * 40)

    print("  🔧 添加user_id字段到visa_status_history表...")
    op.add_column(
        "visa_status_history",
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=True),
    )

    print("  📊 填充visa_status_history.user_id数据...")
    # 通过application和order表填充user_id
    op.execute("""
        UPDATE visa_status_history
        SET user_id = o.user_id
        FROM application a
        JOIN "order" o ON a.order_id = o.id
        WHERE visa_status_history.application_id = a.id
    """)

    print("  🔒 设置visa_status_history.user_id为NOT NULL...")
    op.alter_column("visa_status_history", "user_id", nullable=False)

    print("  🔗 添加visa_status_history.user_id外键约束...")
    op.create_foreign_key(
        "visa_status_history_user_id_fkey",
        "visa_status_history",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )

    print("  📊 创建visa_status_history.user_id索引...")
    op.create_index(
        "ix_visa_status_history_user_id", "visa_status_history", ["user_id"]
    )

    # 第三阶段：创建复合索引优化查询性能
    print("\n📋 第三阶段：性能优化索引")
    print("-" * 40)

    print("  🎯 创建application复合索引...")
    op.create_index(
        "ix_application_user_country", "application", ["user_id", "country"]
    )
    op.create_index(
        "ix_application_user_created", "application", ["user_id", "created_at"]
    )

    print("  🎯 创建visa_status_history复合索引...")
    op.create_index(
        "ix_visa_status_history_user_status",
        "visa_status_history",
        ["user_id", "visa_status"],
    )
    op.create_index(
        "ix_visa_status_history_user_created",
        "visa_status_history",
        ["user_id", "created_at"],
    )

    # 验证修复结果
    print("\n🔍 验证修复结果...")
    print("-" * 40)

    # 检查数据完整性
    connection = op.get_bind()

    # 验证application表
    app_count = connection.execute(
        sa.text("SELECT COUNT(*) FROM application WHERE user_id IS NULL")
    ).scalar()

    if app_count == 0:
        print("  ✅ application表user_id数据完整")
    else:
        print(f"  ❌ application表有{app_count}条记录缺少user_id")

    # 验证visa_status_history表
    history_count = connection.execute(
        sa.text("SELECT COUNT(*) FROM visa_status_history WHERE user_id IS NULL")
    ).scalar()

    if history_count == 0:
        print("  ✅ visa_status_history表user_id数据完整")
    else:
        print(f"  ❌ visa_status_history表有{history_count}条记录缺少user_id")

    print("\n🎉 数据库架构安全修复完成！")
    print("📋 修复摘要：")
    print("  ✅ application表添加user_id字段和索引")
    print("  ✅ visa_status_history表添加user_id字段和索引")
    print("  ✅ 创建外键约束确保数据一致性")
    print("  ✅ 创建复合索引优化查询性能")
    print("  🔐 现在可以直接进行用户权限控制，无需复杂JOIN")


def downgrade():
    """
    回滚架构修复
    """
    print("\n🔄 回滚数据库架构修复...")

    # 删除索引
    op.drop_index("ix_visa_status_history_user_created", "visa_status_history")
    op.drop_index("ix_visa_status_history_user_status", "visa_status_history")
    op.drop_index("ix_application_user_created", "application")
    op.drop_index("ix_application_user_country", "application")
    op.drop_index("ix_visa_status_history_user_id", "visa_status_history")
    op.drop_index("ix_application_user_id", "application")

    # 删除外键约束
    op.drop_constraint(
        "visa_status_history_user_id_fkey", "visa_status_history", type_="foreignkey"
    )
    op.drop_constraint("application_user_id_fkey", "application", type_="foreignkey")

    # 删除字段
    op.drop_column("visa_status_history", "user_id")
    op.drop_column("application", "user_id")

    print("  ✅ 架构修复已回滚")
