from datetime import datetime
import os
from pathlib import Path
import re

from dotenv import load_dotenv
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
from playwright.sync_api import sync_playwright
from pypdf import PdfReader

from app.utils.captcha_solver import solve_captcha
from app.utils.logger_config import get_logger
from app.utils.retry_strategy import evisa_pdf_download_retry
from config.browser_config import launch_form_browser

# 加载环境变量
load_dotenv()

logger = get_logger()


# 🔥 数据库查询函数 - 适配新的数据库结构
def get_application_display_fields(vietnam_application_number: str) -> dict:
    """根据越南官方申请编号获取申请信息（用于下载文件命名）"""
    try:
        import requests

        # 通过FastAPI查询申请信息
        response = requests.get(
            f"http://localhost:8000/api/email-processing/application/{vietnam_application_number}",
            timeout=10,
        )

        if response.status_code == 200:
            data = response.json()
            return {
                "chinese_name": data.get("chinese_name", "Unknown"),
                "applicant_name": data.get("applicant_name", "Unknown"),
                "application_number": vietnam_application_number,
            }
        else:
            logger.warning(f"⚠️ 无法查询申请信息: {response.status_code}")
            return {
                "chinese_name": "Unknown",
                "applicant_name": "Unknown",
                "application_number": vietnam_application_number,
            }

    except Exception as e:
        logger.error(f"❌ 查询申请信息失败: {e}")
        return {
            "chinese_name": "Unknown",
            "applicant_name": "Unknown",
            "application_number": vietnam_application_number,
        }


def get_desktop_download_folder() -> Path:
    """创建日期文件夹（如 20241220越南签证下载）- 支持多环境适配"""
    # 尝试获取桌面路径，如果不存在则使用项目内的downloads目录
    try:
        desktop_path = Path(os.path.expanduser("~/Desktop"))
        logger.debug(f"尝试使用桌面路径: {desktop_path}")

        # 确保桌面目录存在，如果不存在则创建
        if not desktop_path.exists():
            logger.info(f"桌面目录不存在，创建: {desktop_path}")
            desktop_path.mkdir(parents=True, exist_ok=True)

        # 获取当前日期（北京时间）并格式化为YYYYMMDD
        today = datetime.now().strftime("%Y%m%d")
        folder_name = f"{today}越南签证下载"
        download_folder = desktop_path / folder_name

        # 检查是否有同名的文件夹，如果有，就不用创建，如果没有，就创建
        if not download_folder.exists():
            download_folder.mkdir(
                parents=True, exist_ok=True
            )  # 使用parents=True确保创建所有必要的父目录
            logger.info(f"📁 创建新文件夹: {download_folder}")
        else:
            logger.info(f"📁 使用已有文件夹: {download_folder}")

        return download_folder

    except Exception as e:
        # 如果桌面路径无法使用，回退到项目内的downloads目录
        logger.warning(f"⚠️ 无法使用桌面路径，回退到项目目录: {e}")

        # 回退方案：使用项目内的downloads目录
        project_downloads = Path("downloads")
        today = datetime.now().strftime("%Y%m%d")
        folder_name = f"{today}越南签证下载"
        download_folder = project_downloads / folder_name

        # 确保目录存在
        download_folder.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 使用项目下载目录: {download_folder}")

        return download_folder


def get_unique_filepath(target_path: Path) -> Path:
    """确保保存文件不会覆盖已有文件，若同名则递增编号"""
    if not target_path.exists():
        return target_path
    stem, suffix = target_path.stem, target_path.suffix
    parent = target_path.parent
    idx = 1
    while True:
        candidate = parent / f"{stem}({idx}){suffix}"
        if not candidate.exists():
            return candidate
        idx += 1


# 添加重试装饰器
@evisa_pdf_download_retry
def download_pdf_for_application(
    vietnam_application_number: str, url: str
) -> str | None:
    """
    下载签证PDF并按申请人信息重命名

    参数:
        vietnam_application_number: 越南官方申请编号
        url: PDF下载链接

    返回:
        str | None: 下载成功返回文件路径，失败返回None
    """
    logger.debug(
        f"Downloading PDF for application number {vietnam_application_number}..."
    )

    logger.info(f"🚀 开始下载签证PDF，申请编号: {vietnam_application_number}")
    logger.info(f"📌 使用URL: {url}")

    try:
        logger.debug("🔍 获取申请信息用于文件命名...")
        display = get_application_display_fields(
            vietnam_application_number
        )  # 获取重命名字段
        if not display:
            logger.error("❌ 无法获取申请信息用于重命名")
            # 使用默认值继续执行
            display = {
                "chinese_name": "未知申请人",
                "applicant_name": "Unknown Applicant",
                "application_number": vietnam_application_number,
            }
            logger.warning("⚠️ 使用默认文件名继续下载")

        logger.info(
            f"✅ 使用申请信息: {display.get('chinese_name', '')} - {display.get('application_number', '')}"
        )

        # 🔥 简化文件命名逻辑 - 适配新数据库结构
        chinese_name = display.get("chinese_name", "未知申请人")
        applicant_name = display.get("applicant_name", "Unknown Applicant")

        # 优先使用中文名，如果没有则使用英文名
        if chinese_name and chinese_name != "未知申请人":
            clean_name = re.sub(r'[<>:"/\\|?*]', "_", chinese_name)
            final_filename = f"{clean_name}_{vietnam_application_number}_签证.pdf"
        else:
            clean_name = re.sub(r'[<>:"/\\|?*]', "_", applicant_name)
            final_filename = f"{clean_name}_{vietnam_application_number}_visa.pdf"

        logger.info(f"📄 最终文件名: {final_filename}")

        # 构建完整路径
        downloads_dir = Path("downloads")
        downloads_dir.mkdir(exist_ok=True)
        final_path = downloads_dir / final_filename

        # 检查文件是否已存在
        if final_path.exists():
            logger.info(f"⚠️ 文件已存在: {final_path.name}")
            logger.info(f"✅ 返回已存在的文件路径: {final_path}")
            return str(final_path.resolve())

        # 启动Playwright浏览器执行下载
        logger.info("🌐 启动浏览器并导航到签证下载页面...")
        with sync_playwright() as p:
            _, _, page = launch_form_browser(p)
            logger.info("✅ 浏览器已启动")
            logger.info("✅ 浏览器上下文已创建，已启用下载功能")

            # 实现页面加载超时和自动刷新机制
            try:
                # 第一次尝试加载页面，30秒超时
                logger.info("开始加载页面...")
                page.goto(url, timeout=30000)  # 30秒超时
                logger.info("✅ 页面加载完成")
            except Exception as e:
                logger.warning(f"❌页面加载超时 (30秒)，尝试刷新: {e}")
                try:
                    # 刷新页面，再等30秒
                    page.reload(timeout=30000)  # 再给30秒尝试刷新
                    logger.info("✅ 页面刷新加载成功")
                except Exception as refresh_e:
                    # 如果刷新后仍然失败，则抛出异常
                    logger.error(f"❌页面刷新后仍然加载失败: {refresh_e}")
                    raise  # 重新抛出异常，触发外层重试

            # 智能等待验证码图片加载完成
            page.wait_for_selector(
                'img[alt="captcha img"]', state="visible", timeout=300000
            )
            logger.info("✅ 验证码图片已加载")

            for attempt in range(1, 6):
                logger.info(f"🔄 验证码识别尝试 #{attempt}/5")
                try:
                    captcha_element = page.query_selector('img[alt="captcha img"]')
                    if not captcha_element:
                        logger.error("❌ 无验证码图片")
                        raise

                    captcha_img_path = (
                        Path("screenshots")
                        / f"captcha_{vietnam_application_number}_{attempt}.png"
                    )
                    captcha_element.screenshot(path=str(captcha_img_path))
                    logger.info(f"📸 验证码截图已保存: {captcha_img_path}")

                    logger.info("🧠 开始识别验证码...")
                    # captcha_solver.py 负责从环境变量获取API密钥
                    captcha_text = solve_captcha(str(captcha_img_path), page)

                    # 如果验证码识别失败，跳过当前尝试
                    if not captcha_text:
                        logger.warning(f"⚠️ 第 {attempt} 次验证码识别失败，重试")
                        continue
                    logger.info(f"✅ 成功识别验证码: {captcha_text}")

                    # 填写验证码并提交
                    logger.info(f"⌨️ 填写验证码: {captcha_text}")
                    page.fill("input#basic_captcha", captcha_text)

                    logger.info("🖱️ 点击搜索按钮")
                    page.get_by_role("button", name=re.compile("Search", re.I)).click()

                    page.wait_for_selector("text=Get e-Visa", timeout=8000)
                    logger.info("✅ 搜索成功，下载按钮可用")

                    # 等待并触发下载
                    with page.expect_download() as download_info:
                        page.get_by_role(
                            "button", name=re.compile("Get e-Visa", re.I)
                        ).click()
                        logger.info("🖱️ 点击'Get e-Visa'按钮")

                    # 等待下载完成
                    download = download_info.value
                    logger.info("⏳ 正在等待文件保存...")
                    if not download:
                        logger.error("❌ 文件下载失败")
                        logger.debug(f"下载信息：{download_info}")
                        raise Exception("文件下载失败")

                    # 下载完成，保存文件
                    save_path = get_unique_filepath(final_path)
                    download.save_as(str(save_path))
                    logger.info(f"✅ PDF 下载完成并保存为：{save_path.name}")

                    # 验证文件是否真的存在
                    if save_path.exists():
                        file_size = save_path.stat().st_size
                        logger.info(
                            f"✅ 文件验证成功: {save_path.name}, 大小: {file_size} 字节"
                        )
                    else:
                        logger.error(f"❌ 文件保存失败: {save_path.name} 不存在")

                        raise Exception("文件保存失败")

                    # Check PDF integrity
                    try:
                        with open(save_path, "rb") as f:
                            reader = PdfReader(f)
                            _ = len(reader.pages)  # Try to access pages
                            logger.info("✅ 成功检查PDF文件:有效且完整")
                    except Exception as e:
                        logger.error(f"❌ 下载的 PDF 文件无效或不完整: {e}")
                        # Raise to trigger retry
                        raise Exception("下载的 PDF 文件无效或不完整")

                    return str(save_path.resolve())

                except PlaywrightTimeoutError:
                    logger.warning(f"⚠️ 第 {attempt} 次等待失败，重试验证码")
                    continue

        logger.error("❌ 多次验证码失败")
        raise Exception("多次验证码失败")

    except Exception as e:
        logger.warning(f"❌ 下载失败: {e}")
        raise

    finally:
        # 关闭页面和上下文
        logger.info("✅ 清理资源并关闭浏览器")
        # context.close()   # 不需要在这里关闭资源，with sync_playwright() 会自动处理
        # browser.close()  # 不需要在这里关闭资源，with sync_playwright() 会自动处理
