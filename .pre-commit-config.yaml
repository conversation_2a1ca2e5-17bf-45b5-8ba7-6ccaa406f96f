# Pre-commit 配置文件
# 为 visa-automator 项目提供代码质量保障
# 
# 安装: pip install pre-commit
# 激活: pre-commit install
# 运行: pre-commit run --all-files
# 更新: pre-commit autoupdate

repos:
  # ==========================================
  # 通用文件检查
  # ==========================================
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0  # 2025年最新稳定版本
    hooks:
      # 基础文件检查
      - id: trailing-whitespace
        name: 🧹 清理行尾空白字符
        exclude: ^(frontend/|.*\.md$)
      
      - id: end-of-file-fixer
        name: 📝 确保文件以换行符结尾
        exclude: ^(frontend/|.*\.min\.(js|css)$)
      
      - id: check-yaml
        name: ✅ 检查 YAML 文件语法
        exclude: ^frontend/
      
      - id: check-json
        name: ✅ 检查 JSON 文件语法
        exclude: ^frontend/
      
      - id: check-toml
        name: ✅ 检查 TOML 文件语法
      
      - id: check-merge-conflict
        name: 🔍 检查合并冲突标记
      
      - id: check-case-conflict
        name: 🔍 检查文件名大小写冲突
      
      - id: mixed-line-ending
        name: 🔧 统一行结束符
        args: ['--fix=lf']
        exclude: ^frontend/

  # ==========================================
  # Python 代码质量检查
  # ==========================================
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.0  # 与项目中安装的版本一致
    hooks:
      # Ruff 代码检查 (替代 flake8, isort, pyupgrade)
      - id: ruff
        name: 🐍 Ruff 代码检查
        args: [--fix, --exit-non-zero-on-fix]
        exclude: ^(frontend/|alembic/versions/)
      
      # Ruff 代码格式化 (替代 black)
      - id: ruff-format
        name: 🎨 Ruff 代码格式化
        exclude: ^(frontend/|alembic/versions/)

  # ==========================================
  # Python 类型检查 (可选，较严格)
  # ==========================================
  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.7.1
  #   hooks:
  #     - id: mypy
  #       name: 🔍 MyPy 类型检查
  #       exclude: ^(frontend/|tests/|alembic/)
  #       additional_dependencies: [types-requests, types-PyYAML]

  # ==========================================
  # 安全检查
  # ==========================================
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.0  # 2025年最新稳定版本
    hooks:
      - id: bandit
        name: 🔒 Bandit 安全检查
        args: [-r, -f, json, -o, bandit-report.json]
        exclude: ^(frontend/|tests/|alembic/)

  # ==========================================
  # 前端代码检查 (可选)
  # ==========================================
  # 注意：需要 Node.js 环境
  # - repo: https://github.com/pre-commit/mirrors-eslint
  #   rev: v8.56.0
  #   hooks:
  #     - id: eslint
  #       name: 🌐 ESLint 前端检查
  #       files: ^frontend/.*\.(js|ts|vue)$
  #       additional_dependencies:
  #         - eslint@8.56.0
  #         - '@typescript-eslint/parser@6.14.0'
  #         - '@typescript-eslint/eslint-plugin@6.14.0'
  #         - eslint-plugin-vue@9.19.2

  # ==========================================
  # 文档和配置文件检查
  # ==========================================
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.37.1  # 2025年最新稳定版本
    hooks:
      - id: yamllint
        name: 📋 YAML 文件格式检查
        exclude: ^frontend/
        args: [-d, '{extends: relaxed, rules: {line-length: {max: 120}}}']

  # ==========================================
  # 敏感信息检查
  # ==========================================
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0  # 2025年最新稳定版本
    hooks:
      - id: detect-secrets
        name: 🔐 检测敏感信息泄露
        args: ['--baseline', '.secrets.baseline']
        exclude: ^(frontend/node_modules/|\.git/)

  # ==========================================
  # 大文件检查
  # ==========================================
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0  # 2025年最新稳定版本
    hooks:
      - id: check-added-large-files
        name: 📦 检查大文件
        args: ['--maxkb=1000']  # 限制 1MB
        exclude: ^(frontend/node_modules/|downloads/|screenshots/|temp/)

# ==========================================
# 全局配置
# ==========================================
default_stages: [commit]
fail_fast: false  # 不在第一个错误时停止
minimum_pre_commit_version: '3.0.0'

# 排除的文件和目录
exclude: |
  (?x)^(
    frontend/node_modules/.*|
    frontend/dist/.*|
    .*\.min\.(js|css)$|
    downloads/.*|
    screenshots/.*|
    temp/.*|
    logs/.*|
    __pycache__/.*|
    \.git/.*|
    venv/.*|
    \.venv/.*
  )$

# CI 配置
ci:
  autofix_commit_msg: |
    🤖 [pre-commit.ci] 自动修复代码格式
    
    由 pre-commit.ci 自动应用的修复
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '🔄 [pre-commit.ci] 更新 pre-commit hooks'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
