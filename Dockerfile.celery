# 2025年最佳实践：安全的Celery Worker Dockerfile
# 参考：Docker官方安全指南和Celery最佳实践
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 🔧 2025年安全最佳实践：先创建非root用户
# 使用固定的UID/GID，避免权限冲突
RUN groupadd --system --gid 1001 celery && \
    useradd --system --uid 1001 --gid celery --home /app --shell /bin/bash celery

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV REDIS_HOST=redis
ENV TZ=Asia/Shanghai

# 设置时区为上海时间（与Python和数据库保持一致）
RUN ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime && echo "$TZ" > /etc/timezone

# 复制依赖文件，设置正确的所有者
COPY --chown=celery:celery pyproject.toml .

# 🔧 最佳实践2：为Playwright指定一个系统级的、与用户无关的安装路径
#    这是解决问题的关键！我们把浏览器安装在 /ms-playwright 这个"公共公园"里。
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# 安装Python依赖和Playwright浏览器（在root权限下）
RUN pip install --no-cache-dir -e . && \
    playwright install --with-deps chromium

# 复制所有项目代码，设置正确的所有者
COPY --chown=celery:celery . .

# 创建必要的目录，设置正确的权限
RUN mkdir -p screenshots payment_screenshots downloads .prefs test_data results && \
    chown -R celery:celery /app

# 🔧 2025年安全最佳实践：切换到非root用户
# 注意：浏览器已经安装在系统级目录，celery用户只需要读取权限
USER celery

# 🔥 生产级 Celery Worker 启动配置
# 优化并发数、心跳、日志等参数以支持大规模部署

# 🎯 标准配置：每个容器4个进程（推荐用于浏览器自动化）
CMD ["celery", "-A", "celery_worker.celery_app", "worker", \
    "--loglevel=info", \
    "--pool=prefork", \
    "--concurrency=4", \
    "--heartbeat-interval=30", \
    "--without-gossip", \
    "--without-mingle", \
    "--optimization=fair"]
