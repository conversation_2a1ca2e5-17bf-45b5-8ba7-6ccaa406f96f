#!/usr/bin/env python3
"""
独立邮件轮询服务
================

专门负责邮件轮询、解析和处理的独立服务
与主应用和Celery Worker完全分离，避免容器扩展时的重复轮询问题

特性：
- 单一职责：只负责邮件轮询
- 独立部署：不依赖其他服务容器
- HTTP通知：通过API与FastAPI通信
- 资源优化：避免重复轮询和资源浪费
"""

from datetime import datetime
from http.server import BaseHTTPRequestHandler, HTTPServer
import json
import os
import signal
import sys
import threading
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath("/app"))

from dotenv import load_dotenv

from app.utils.env_loader import load_email_accounts_from_env
from app.utils.logger_config import get_logger, setup_logger

# 加载环境变量
load_dotenv()

# 设置日志
setup_logger(console_level="INFO")
logger = get_logger()


class HealthCheckHandler(BaseHTTPRequestHandler):
    """健康检查HTTP处理器"""

    def do_GET(self):
        if self.path == "/health":
            try:
                # 获取全局服务实例的健康状态
                if hasattr(self.server, "email_service") and self.server.email_service:
                    status = self.server.email_service.health_check()
                    if status.get("status") == "healthy":
                        self.send_response(200)
                        self.send_header("Content-type", "application/json")
                        self.end_headers()
                        self.wfile.write(json.dumps(status).encode())
                    else:
                        self.send_response(503)
                        self.send_header("Content-type", "application/json")
                        self.end_headers()
                        self.wfile.write(json.dumps(status).encode())
                else:
                    self.send_response(503)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    self.wfile.write(
                        json.dumps(
                            {
                                "status": "not_initialized",
                                "message": "Email service not initialized",
                            }
                        ).encode()
                    )
            except Exception as e:
                self.send_response(500)
                self.send_header("Content-type", "application/json")
                self.end_headers()
                self.wfile.write(
                    json.dumps({"status": "error", "error": str(e)}).encode()
                )
        else:
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        # 禁用默认日志输出
        pass


class SimpleApplicant:
    """简化的申请人类，用于邮件轮询"""

    def __init__(self, email, config):
        self.email = email
        self.config = config
        self.passport_number = f"email_{email.split('@')[0]}"
        self.chinese_name = email.split("@")[0]  # 简化的中文名


class EmailPollingService:
    """独立邮件轮询服务"""

    def __init__(self):
        self.running = False
        self.scheduler = None
        self.thread_pool = None
        self.applicants = []
        self.health_server = None
        self.health_thread = None

    def load_email_accounts(self):
        """加载邮箱账户配置"""
        try:
            logger.info("🔍 加载邮箱账户配置...")

            email_configs_data = load_email_accounts_from_env()
            email_accounts_map = email_configs_data.get("email_accounts", {})

            self.applicants = []
            if email_accounts_map:
                for email_user, config_dict in email_accounts_map.items():
                    if (
                        config_dict
                        and isinstance(config_dict, dict)
                        and "user" in config_dict
                        and config_dict["user"] == email_user
                    ):
                        applicant = SimpleApplicant(
                            email=email_user, config=config_dict
                        )
                        self.applicants.append(applicant)
                        logger.info(f"✅ 加载邮箱账户: {email_user}")
                    else:
                        logger.warning(f"⚠️ 跳过无效的邮箱配置: {email_user}")
            else:
                logger.warning("⚠️ 环境变量中未找到邮箱账户配置")

            logger.info(f"📊 总共加载 {len(self.applicants)} 个邮箱账户")
            return len(self.applicants) > 0

        except Exception as e:
            logger.error(f"❌ 加载邮箱账户配置失败: {e}", exc_info=True)
            return False

    def start_polling_scheduler(self):
        """启动邮件轮询调度器"""
        try:
            logger.info("🚀 启动邮件轮询调度器...")

            # 导入统一邮件轮询服务
            from app.email.unified_email_service import UnifiedEmailService

            self.unified_service = UnifiedEmailService()

            # 启动统一邮件服务
            if self.unified_service.start_service():
                logger.info(
                    f"✅ 统一邮件轮询服务启动成功，监控 {len(self.applicants)} 个邮箱"
                )
                return True
            else:
                logger.warning("⚠️ 统一邮件轮询服务启动失败")
                return False

        except Exception as e:
            logger.error(f"❌ 启动邮件轮询调度器失败: {e}", exc_info=True)
            return False

    def stop_polling_scheduler(self):
        """停止邮件轮询调度器"""
        try:
            logger.info("🛑 停止统一邮件轮询服务...")

            if hasattr(self, "unified_service") and self.unified_service:
                self.unified_service.stop_service()
                logger.info("✅ 统一邮件轮询服务已停止")

        except Exception as e:
            logger.error(f"⚠️ 停止邮件服务时出错: {e}")

    def start_health_server(self):
        """启动HTTP健康检查服务器"""
        try:
            self.health_server = HTTPServer(("0.0.0.0", 8001), HealthCheckHandler)
            self.health_server.email_service = self  # 传递服务实例

            def run_server():
                logger.info("🏥 健康检查服务器启动在端口 8001")
                self.health_server.serve_forever()

            self.health_thread = threading.Thread(target=run_server, daemon=True)
            self.health_thread.start()

        except Exception as e:
            logger.error(f"❌ 启动健康检查服务器失败: {e}")

    def stop_health_server(self):
        """停止HTTP健康检查服务器"""
        try:
            if self.health_server:
                self.health_server.shutdown()
                logger.info("🏥 健康检查服务器已停止")
        except Exception as e:
            logger.error(f"⚠️ 停止健康检查服务器时出错: {e}")

    def health_check(self):
        """健康检查"""
        try:
            if hasattr(self, "unified_service") and self.unified_service:
                return self.unified_service.health_check()
            else:
                return {
                    "service": "email_polling_service",
                    "status": "not_initialized",
                    "timestamp": datetime.now().isoformat(),
                    "accounts_count": len(self.applicants),
                }

        except Exception as e:
            logger.error(f"❌ 健康检查失败: {e}")
            return {
                "service": "email_polling_service",
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            }

    def run(self):
        """运行邮件轮询服务"""
        logger.info("🌟 启动独立邮件轮询服务")
        logger.info("=" * 50)

        # 设置信号处理
        def signal_handler(signum, _):
            logger.info(f"📡 接收到信号 {signum}，准备优雅关闭...")
            self.running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        try:
            # 启动健康检查服务器
            self.start_health_server()

            # 加载邮箱配置
            if not self.load_email_accounts():
                logger.error("❌ 无法加载邮箱配置，服务退出")
                return 1

            # 启动轮询调度器
            if not self.start_polling_scheduler():
                logger.error("❌ 无法启动轮询调度器，服务退出")
                return 1

            self.running = True
            logger.info("✅ 邮件轮询服务启动成功")
            logger.info("📧 开始监控邮箱...")

            # 主循环
            while self.running:
                try:
                    # 定期健康检查和状态报告
                    status = self.health_check()
                    if status["status"] == "healthy":
                        logger.debug(
                            f"💓 服务健康 - 监控 {status['accounts_count']} 个邮箱，"
                            f"{status['jobs_count']} 个调度任务"
                        )
                    else:
                        logger.warning(f"⚠️ 服务状态异常: {status}")

                    # 等待30秒
                    time.sleep(30)

                except KeyboardInterrupt:
                    logger.info("👋 接收到中断信号，准备退出...")
                    break
                except Exception as e:
                    logger.error(f"❌ 主循环异常: {e}", exc_info=True)
                    time.sleep(10)  # 异常后等待10秒再继续

        except Exception as e:
            logger.error(f"❌ 邮件轮询服务启动失败: {e}", exc_info=True)
            return 1

        finally:
            # 清理资源
            logger.info("🧹 清理资源...")
            self.stop_polling_scheduler()
            self.stop_health_server()
            logger.info("👋 邮件轮询服务已退出")

        return 0


def main():
    """主函数"""
    # 设置控制台输出编码为 UTF-8
    import contextlib

    with contextlib.suppress(Exception):
        sys.stdout.reconfigure(encoding="utf-8")

    # 创建并运行服务
    service = EmailPollingService()
    exit_code = service.run()

    sys.exit(exit_code)


if __name__ == "__main__":
    main()
