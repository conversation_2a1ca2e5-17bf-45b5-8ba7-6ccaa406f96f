# Locators for Vietnam E-Visa Application Website Automation
# -----------------------------------------------------
# !!! 警告：请务必用浏览器开发者工具 (F12) 仔细核对以下所有定位器值 !!!
# !!! 依赖文本的定位器 (:has-text) 对网站文字变更非常敏感，可能失效 !!!
# -----------------------------------------------------

# --- 阶段 1: 主页 ---
homepage:
  apply_now_button: 'button:has-text("Apply now")' # Action: 核对精确文本

# --- 阶段 2: 同意条款弹窗 (Modal) ---
terms_modal:
  scrollable_container: "div[data-v-029e2e88] div.overflow-y-scroll" # 注意: data-v-* 属性可能不稳定
  accept_terms_checkbox: >-
    label.ant-checkbox-wrapper:has-text("Confirm compliance with Vietnamese laws upon entry")
  confirm_read_checkbox: >-
    label.ant-checkbox-wrapper:has-text("Confirmation of reading carefully
    instructions and having completed application")
  next_button: 'button:has-text("Next")' # Action: 核对精确文本

# --- 阶段 4: 验证码页面 ---
captcha_page:
  captcha_input: "#basic_captcha" # 验证码输入框
  captcha_image: 'img[src^="data:image/png;base64"][width="240"][height="100"]' # 精确匹配验证码图片
  refresh_button: ".anticon-reload" # 刷新验证码按钮
  next_button: 'button.bg-evisa-main9:has-text("Next")' # 更精确的下一步按钮定位
  error_message: ".ant-form-item-explain-error" # 验证码错误信息

# --- 阶段 3: 主申请表单 ---
main_form:
  # 用于滚动或等待的表单锚点元素
  anchor_element: "#basic_ttcnHo"

  # --- 表单最下方的Next按钮 ---
  next_button: 'button[type="button"]:has-text("Next")'

  # --- 3A: 文件上传 ---
  file_uploads:
    portrait_photo_input: "#basic_anhMat"
    passport_scan_input: "#basic_anhHoChieu"

  # --- 3B: 个人信息 ---
  personal_info:
    surname_input: "#basic_ttcnHo" # 注意: 也用作表单锚点
    given_name_input: "#basic_ttcnDemVaTen"
    dob_input: "#basic_ttcnNgayThangNamSinhStr" # 注意: 日期选择器 / OCR目标
    sex_select: ".ant-form-item:has(#basic_ttcnGioiTinh) .ant-select-selector" # 注意: 下拉框触发器 / OCR目标
    sex_option_template: '.ant-select-item-option-content:text-is("{option_text}")' # 模板: 用于选择性别选项 (精确匹配)
    nationality_select: ".ant-form-item:has(#basic_ttcnMaQt) .ant-select-selector" # 注意: 下拉框触发器 / OCR目标
    nationality_option_template: '.ant-select-item-option-content:has-text("{option_text}")' # 模板: 用于选择国籍选项
    place_of_birth_input: "#basic_ttcnNoiSinh" # Action: 确认此ID准确性
    religion_input: "#basic_ttcnTonGiao" # Action: 填充 "NO"

  # --- 3C: 护照信息 ---
  passport_details:
    passport_no_input: "#basic_hcSo" # 注意: OCR目标
    passport_type_select: ".ant-form-item:has(#basic_hcLoai) .ant-select-selector" # Action: 需要选择
    passport_type_option_template: '.ant-select-item-option-content:has-text("{option_text}")' # 模板: 用于选择护照类型选项
    date_of_issue_input: "#basic_hcNgayCapStr" # 注意: 日期选择器
    passport_expiry_input: "#basic_hcGiaTriDenStr" # 注意: 日期选择器 / OCR目标

  # --- 3D: 联系信息 ---
  contact_info:
    email_input: "#basic_ttcnEmail"
    re_enter_email_input: "#basic_ttcnConfirmEmail"
    telephone_input: "#basic_ttllSdt" # 注意: 国际格式电话号码
    permanent_address_input: "#basic_ttllDcThuongTru"
    contact_address_input: "#basic_ttllDcLienHe" # 注意: 通常与永久地址相同
    address_fields:
      permanent_address: "#basic_ttllDcThuongTru"
      contact_address: "#basic_ttllDcLienHe"
      emergency_address: "#basic_ttllLlNoiOHienTai"

    # --- (越南境内地址 - V1跳过) ---
    #vn_residence_address_select: '.ant-form-item:has(#basic_ttcdDcTamTru) .ant-select-selector' # (V1 跳过)
    #vn_province_city_select: '.ant-form-item:has(#basic_ttcdTinhTp) .ant-select-selector'     # (V1 跳过)
    #vn_district_select: '.ant-form-item:has(#basic_ttcdQuanHuyen) .ant-select-selector'      # (V1 跳过)
    #vn_ward_commune_select: '.ant-form-item:has(#basic_ttcdPhuongXa) .ant-select-selector'   # (V1 跳过)
    #vn_phone_input: '#basic_ttcdSdt'                   # (V1 跳过 - 越南境内电话)

  # --- 3E: 旅行信息 ---
  travel_info:
    purpose_of_entry_select: ".ant-form-item:has(#basic_ttcdMucDich) .ant-select-selector" # Action: 需要选择 (通常 "Tourist")
    purpose_of_entry_option_template: '.ant-select-item-option-content:has-text("{option_text}")' # 模板: 用于选择目的选项
    intended_date_of_entry_input: "#basic_ttcdThoiGianNcStr" # 注意: 只读日期选择器 (V1 跳过)
    intended_length_of_stay_input: "#basic_ttcdSoNgayTamTru" # 注意: 文本输入 (V1 跳过)
    visa_valid_from_input: "#basic_nddnTtdtTuNgayStr" # 注意: 日期选择器 (需点击后填充)
    visa_valid_to_input: "#basic_nddnTtdtDenNgayStr" # 注意: 日期选择器 (需点击后填充)
    visa_type_single_entry_radio: 'label.ant-radio-wrapper:has-text("Single-entry")' # Action: 核对文本并选择
    visa_type_multiple_entry_radio: 'label.ant-radio-wrapper:has-text("Multiple-entry")' # Action: 核对文本并选择
    intended_entry_gate_select: ".ant-form-item:has(#basic_ttcdNcCuaKhau) .ant-select-selector" # Action: 需要选择
    intended_entry_gate_option_template: '.ant-select-item-option-content:has-text("{option_text}")' # 模板: 用于选择入境口岸
    intended_entry_gate_input: "#basic_ttcdNcCuaKhau" # 保留ID供参考
    intended_exit_gate_select: ".ant-form-item:has(#basic_ttcdXcCuaKhau) .ant-select-selector" # Action: 需要选择 (通常同入境口岸)
    intended_exit_gate_option_template: '.ant-select-item-option-content:has-text("{option_text}")' # 模板: 用于选择出境口岸
    intended_exit_gate_input: "#basic_ttcdXcCuaKhau" # 保留ID供参考

  # --- 3F: 紧急联系人 ---
  emergency_contact:
    fullname_input: "#basic_ttllLlHoTen"
    phone_input: "#basic_ttllLlSdt" # 注意: 与主要电话字段ID模式相似，需验证上下文
    address_input: "#basic_ttllLlNoiOHienTai" # 注意: 这是紧急联系人当前地址
    relationship_input: "#basic_ttllLlQuanHe"

  # --- 3G: 过往访问记录 (近一年内) ---
  previous_visits:
    # 定位包含问题的整个div，以便在其内部查找 Yes/No
    question_section: 'div.ant-col:has(div:has-text("Have you been to Viet Nam in the last 01 year?"))' # Action: 核对问题文本
    visited_last_year_no_radio: 'label.ant-radio-wrapper:has-text("No")' # 注意: 通常需要先定位到 question_section
    visited_last_year_yes_radio: 'label.ant-radio-wrapper:has-text("Yes")' # 注意: 通常需要先定位到 question_section
    # --- (仅当选择 "Yes" 时出现) ---
    visit_details: # 嵌套一层表示这些是依赖于上面选项的
      from_date_input: "#basic_tungDenVn_0_tuNgayStr" # 注意: 日期选择器
      to_date_input: "#basic_tungDenVn_0_denNgayStr" # 注意: 只读日期选择器?
      purpose_input: "#basic_tungDenVn_0_mucDich"

  # --- 3H: 越南联系组织/个人 ---
  vietnam_contact:
    # 定位包含问题的整个div，以便在其内部查找 Yes/No
    question_section: >-
      div.ant-col:has(div:has-text("Agency/Organization/Individual that the applicant plans to contact when enter into Viet Nam?"))
    has_contact_no_radio: >-
      div.ant-col:has-text("Agency/Organization/Individual that the applicant plans to contact when enter into Viet Nam?") input[type="radio"][value="0"]
    has_contact_yes_radio: >-
      div.ant-col:has-text("Agency/Organization/Individual that the applicant plans to contact when enter into Viet Nam?") input[type="radio"][value="1"]
    # --- (仅当选择 "Yes" 时出现) ---
    contact_details: # 嵌套一层表示这些是依赖于上面选项的
      organization_name_input: "#basic_cqTcLienHe_0_cqTc" # Name of hosting organization
      telephone_input: "#basic_cqTcLienHe_0_sdt" # Telephone number
      address_input: "#basic_cqTcLienHe_0_diaChi" # Address
      purpose_input: "#basic_cqTcLienHe_0_mucDich" # Purpose

  # --- 3I: 声明与最终确认 ---
  declarations:
    # 注意: 默认可能是勾选的，自动化流程可能需要取消勾选此项
    agree_email_account_checkbox: >-
      label.ant-checkbox-wrapper:has-text("Agree to create account by email")
    commit_temp_residence_checkbox: "#basic_ttcdCqTcCamDoan" # Action: 需要勾选
    final_declaration_checkbox: >-
      label.ant-checkbox-wrapper:has-text("I hereby declare that the above statements are true")

  # --- (可选) 提交按钮 ---
  # submit_button: 'button:has-text("Review application")' # Action: 核对精确文本
  # 越南地址表单字段
  vietnam_address:
    # 街道地址 - 纯文本输入框
    residential_address_input: "#basic_ttcdDcTamTru"

    # 省市 - 带下拉选择的输入框
    province_city_input: "#basic_ttcdTinhTp"
    province_city_select: ".ant-select-selector:has(#basic_ttcdTinhTp)"

    # 区 - 带下拉选择的输入框
    district_input: "#basic_ttcdQuanHuyen"
    district_select: ".ant-select-selector:has(#basic_ttcdQuanHuyen)"

    # 乡/镇 - 带下拉选择的输入框
    ward_commune_input: "#basic_ttcdPhuongXa"
    ward_commune_select: ".ant-select-selector:has(#basic_ttcdPhuongXa)"
