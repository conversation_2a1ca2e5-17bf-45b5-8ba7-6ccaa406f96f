"""merge visa status and automation logs fixes

Revision ID: ffc2e87fe465
Revises: fix_automation_logs_constraint, simplify_visa_status
Create Date: 2025-06-22 04:11:44.802130

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "ffc2e87fe465"  # pragma: allowlist secret
down_revision: Union[str, tuple[str, ...], None] = (
    "fix_automation_logs_constraint",
    "simplify_visa_status",
)
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
