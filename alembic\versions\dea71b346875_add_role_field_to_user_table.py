"""add role field to user table

彻底移除orders、order_sequence、order_status_history等历史表相关迁移，仅保留user表相关迁移内容。

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "dea71b346875"
down_revision: Union[str, None] = "92c41a319125"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user", sa.Column("role", sa.String(length=32), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "role")
    # ### end Alembic commands ###
