"""
修复数据库表格排列混乱无逻辑的问题

Revision ID: fix_table_organization
Revises: simplify_status_system
Create Date: 2025-06-19

修复问题：
1. applicant表：place_of_birth后接着护照信息，passport_type放在最后位置不合理
2. application表：删除遗留的visa_type_id字段
3. automation_logs表：task_status应只有3个最终状态(success,failed,cancelled)，不应包含processing
4. 验证visa_status_history表状态是否符合5状态设计
5. 重新按逻辑分组排列所有表字段，提高可维护性

字段分组逻辑：
- 标识字段 → 核心业务字段 → 辅助信息字段 → 系统字段（时间戳）
- 相关字段就近原则（如护照信息集中，联系信息集中）
"""

from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = "fix_table_organization"
down_revision = "simplify_status_system"
branch_labels = None
depends_on = None


def upgrade():
    print("📋 开始修复数据库表格结构...")
    print("🔍 当前问题分析：")
    print("  - applicant表：passport_type在第25位，应与其他护照信息集中")
    print("  - application表：visa_type_id为遗留字段，需删除")
    print("  - automation_logs表：processing非最终状态，需移除")
    print("  - 各表字段缺乏逻辑分组")
    print()

    # 1. 检查并删除application表的遗留visa_type_id字段
    print("🗑️ 清理application表遗留字段...")
    connection = op.get_bind()

    # 检查字段是否存在
    result = connection.execute(
        sa.text("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'application' AND column_name = 'visa_type_id'
    """)
    )

    if result.fetchone():
        print("  📍 发现visa_type_id字段，正在删除...")
        # 直接删除字段（之前已确认没有外键约束）
        op.drop_column("application", "visa_type_id")
        print("  ✅ 成功删除application.visa_type_id字段")
    else:
        print("  ✅ visa_type_id字段不存在，无需删除")

    # 2. 修改automation_logs的task_status约束
    print("🔧 修复automation_logs表task_status约束...")

    # 检查当前processing记录数量
    processing_count = connection.execute(
        sa.text("SELECT COUNT(*) FROM automation_logs WHERE task_status = 'processing'")
    ).scalar()
    print(f"  📊 当前processing状态记录数：{processing_count}")

    # 更新所有processing状态为failed（因为processing不是最终状态）
    if processing_count > 0:
        print("  🔄 更新processing状态记录为failed...")
        result = connection.execute(
            sa.text("""
                UPDATE automation_logs
                SET task_status = 'failed',
                    completed_at = COALESCE(completed_at, updated_at, now()),
                    error_message = COALESCE(error_message, '自动修正：processing状态转为failed')
                WHERE task_status = 'processing'
            """)
        )
        print(f"  ✅ 更新了 {result.rowcount} 条processing记录为failed状态")

    # 删除旧的约束
    try:
        op.drop_constraint(
            "check_completion_time_logic", "automation_logs", type_="check"
        )
        print("  ✅ 删除旧的completion_time约束")
    except Exception as e:
        print(f"  ⚠️ 删除旧约束失败（可能不存在）: {e}")

    # 创建新的task_status约束（只允许3个最终状态）
    op.create_check_constraint(
        "automation_task_status_check",
        "automation_logs",
        "task_status IN ('success', 'failed', 'cancelled')",
    )
    print("  ✅ 创建新约束：只允许success、failed、cancelled三个最终状态")

    # 创建新的完成时间逻辑约束
    op.create_check_constraint(
        "automation_completion_required",
        "automation_logs",
        "(task_status IN ('success', 'failed', 'cancelled') AND completed_at IS NOT NULL) OR (task_status = 'processing' AND completed_at IS NULL)",
    )
    print("  ✅ 创建完成时间约束：所有记录必须有completed_at")

    # 3. 重新组织applicant表字段顺序（护照信息集中）
    print("🔄 重新组织applicant表字段顺序...")
    print("  📝 问题：passport_type在第25位，与其他护照信息分离")
    print("  🎯 目标：按逻辑分组 - 标识→个人信息→护照信息→联系信息→工作信息→时间戳")

    # 创建新的applicant表结构（优化字段排列）
    op.execute("""
        CREATE TABLE applicant_new (
            -- 🔑 基本标识
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL,

            -- 👤 个人基本信息
            surname VARCHAR(64) NOT NULL,
            given_name VARCHAR(64) NOT NULL,
            chinese_name VARCHAR(64),
            sex VARCHAR(8),
            date_of_birth DATE,
            place_of_birth VARCHAR(128),
            nationality VARCHAR(32),

            -- 📘 护照信息
            passport_type VARCHAR(32),
            passport_number VARCHAR(32) NOT NULL,
            date_of_issue DATE,
            date_of_expiry DATE,
            place_of_issue VARCHAR(128),

            -- 📞 联系信息
            telephone_number VARCHAR(32),
            email VARCHAR(128),
            permanent_address VARCHAR(256),
            contact_address VARCHAR(256),

            -- 🏢 工作信息
            work_unit VARCHAR(128),
            work_address VARCHAR(256),

            -- 🚨 紧急联系人信息
            emergency_contact_name VARCHAR(64),
            emergency_contact_phone VARCHAR(32),
            emergency_address VARCHAR(256),

            -- ⏰ 时间戳
            created_at TIMESTAMP DEFAULT now(),
            updated_at TIMESTAMP DEFAULT now()
        )
    """)

    # 安全迁移数据
    print("  📊 开始数据迁移...")
    migration_result = connection.execute(
        sa.text("""
        INSERT INTO applicant_new (
            id, user_id, surname, given_name, chinese_name, sex, date_of_birth,
            place_of_birth, nationality, passport_type, passport_number, date_of_issue,
            date_of_expiry, place_of_issue, telephone_number, email, permanent_address,
            contact_address, work_unit, work_address, emergency_contact_name,
            emergency_contact_phone, emergency_address, created_at, updated_at
        )
        SELECT
            id, user_id, surname, given_name, chinese_name, sex, date_of_birth,
            place_of_birth, nationality, passport_type, passport_number, date_of_issue,
            date_of_expiry, place_of_issue, telephone_number, email, permanent_address,
            contact_address, work_unit, work_address, emergency_contact_name,
            emergency_contact_phone, emergency_address, created_at, updated_at
        FROM applicant
    """)
    )
    print(f"  ✅ 迁移了 {migration_result.rowcount} 条申请人记录")

    # 安全删除旧表并重命名
    print("  🔄 替换旧表...")
    print("  🔗 删除依赖applicant表的外键约束...")
    op.drop_constraint(
        "application_applicant_id_fkey", "application", type_="foreignkey"
    )

    op.drop_table("applicant")
    op.execute("ALTER TABLE applicant_new RENAME TO applicant")

    # 重新创建外键和约束
    print("  🔗 重建约束和外键...")
    op.create_foreign_key(
        "applicant_user_id_fkey",
        "applicant",
        "user",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "application_applicant_id_fkey",
        "application",
        "applicant",
        ["applicant_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_unique_constraint(
        "uq_user_passport", "applicant", ["user_id", "passport_number"]
    )

    print(
        "  ✅ applicant表重组完成！新顺序：标识→个人信息→护照信息(集中)→联系→工作→紧急联系人→时间戳"
    )

    # 4. 跳过user表重新组织（保持原有字段顺序）
    print("⏩ 跳过user表重新组织，保持原有字段顺序不变")

    # 5. 重新组织application表字段顺序（删除visa_type_id，按业务逻辑分组）
    print("🔄 重新组织application表字段顺序...")
    print("  📝 问题：字段排列混乱，visa_type_id遗留字段已删除")
    print("  🎯 目标：标识→签证基本信息→签证详细信息→越南历史→联系人→系统信息→时间戳")

    op.execute("""
        CREATE TABLE application_new (
            -- 🔑 基本标识
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            order_id UUID NOT NULL,
            applicant_id UUID NOT NULL,

            -- 🌍 签证基本信息
            country VARCHAR(3),
            category VARCHAR(32),

            -- 📋 签证详细信息
            visa_entry_type VARCHAR(32),
            visa_validity_duration VARCHAR(32),
            visa_start_date DATE,
            intended_entry_gate VARCHAR(128),
            purpose_of_entry VARCHAR(128),

            -- 🇻🇳 越南历史信息
            visited_vietnam_last_year BOOLEAN,
            previous_entry_date DATE,
            previous_exit_date DATE,
            previous_purpose VARCHAR(128),

            -- 👥 越南联系人信息
            has_vietnam_contact BOOLEAN,
            vietnam_contact_organization VARCHAR(128),
            vietnam_contact_phone VARCHAR(32),
            vietnam_contact_address VARCHAR(256),
            vietnam_contact_purpose VARCHAR(128),

            -- 💾 系统信息
            form_snapshot JSON NOT NULL,
            vietnam_application_number VARCHAR(64),

            -- ⏰ 时间戳
            created_at TIMESTAMP DEFAULT now(),
            updated_at TIMESTAMP DEFAULT now()
        )
    """)

    # 迁移申请数据（排除visa_type_id）
    print("  📊 开始数据迁移...")
    app_migration_result = connection.execute(
        sa.text("""
        INSERT INTO application_new (
            id, order_id, applicant_id, country, category, visa_entry_type, visa_validity_duration, visa_start_date,
            intended_entry_gate, purpose_of_entry, visited_vietnam_last_year,
            previous_entry_date, previous_exit_date, previous_purpose, has_vietnam_contact,
            vietnam_contact_organization, vietnam_contact_phone, vietnam_contact_address,
            vietnam_contact_purpose, form_snapshot, vietnam_application_number,
            created_at, updated_at
        )
        SELECT
            id, order_id, applicant_id, country, category, visa_entry_type, visa_validity_duration, visa_start_date,
            intended_entry_gate, purpose_of_entry, visited_vietnam_last_year,
            previous_entry_date, previous_exit_date, previous_purpose, has_vietnam_contact,
            vietnam_contact_organization, vietnam_contact_phone, vietnam_contact_address,
            vietnam_contact_purpose, form_snapshot, vietnam_application_number,
            created_at, updated_at
        FROM application
    """)
    )
    print(f"  ✅ 迁移了 {app_migration_result.rowcount} 条申请记录")

    # 删除旧表，重命名新表
    print("  🔄 替换旧表...")
    print("  🔗 删除依赖application表的外键约束...")
    op.drop_constraint("file_application_id_fkey", "file", type_="foreignkey")
    op.drop_constraint(
        "visa_payment_application_id_fkey", "visa_payment", type_="foreignkey"
    )
    op.drop_constraint(
        "visa_status_history_application_id_fkey",
        "visa_status_history",
        type_="foreignkey",
    )
    op.drop_constraint(
        "automation_logs_application_id_fkey", "automation_logs", type_="foreignkey"
    )

    op.drop_table("application")
    op.execute("ALTER TABLE application_new RENAME TO application")

    # 重新创建外键约束
    print("  🔗 重建外键约束...")
    op.create_foreign_key(
        "application_order_id_fkey",
        "application",
        "order",
        ["order_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "application_applicant_id_fkey",
        "application",
        "applicant",
        ["applicant_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "file_application_id_fkey",
        "file",
        "application",
        ["application_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "visa_payment_application_id_fkey",
        "visa_payment",
        "application",
        ["application_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "visa_status_history_application_id_fkey",
        "visa_status_history",
        "application",
        ["application_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "automation_logs_application_id_fkey",
        "automation_logs",
        "application",
        ["application_id"],
        ["id"],
        ondelete="CASCADE",
    )

    print(
        "  ✅ application表重组完成！新顺序：标识→签证基本信息→签证详细→越南历史→联系人→系统信息→时间戳"
    )
    print("  🗑️ 已删除selected_validity_duration和selected_entry_type字段")

    # 6. 验证修复结果
    print("🔍 验证修复结果...")

    # 检查visa_status_history状态是否符合5状态设计
    visa_statuses = connection.execute(
        sa.text("""
        SELECT DISTINCT to_status, COUNT(*) as count
        FROM visa_status_history
        GROUP BY to_status
        ORDER BY to_status
    """)
    ).fetchall()

    print("  📊 visa_status_history当前状态分布：")
    expected_statuses = {
        "submit_failure",
        "submitted",
        "additional_info_required",
        "approved",
        "denied",
    }
    actual_statuses = {status[0] for status in visa_statuses}

    for status, count in visa_statuses:
        print(f"    - {status}: {count}条记录")

    if actual_statuses == expected_statuses:
        print("  ✅ visa_status_history状态完全符合5状态设计")
    elif actual_statuses.issubset(expected_statuses):
        print("  ✅ visa_status_history状态符合5状态设计（部分状态暂无数据）")
    else:
        unexpected = actual_statuses - expected_statuses
        print(f"  ⚠️ 发现意外状态：{unexpected}")

    print("\n🎉 数据库表格结构修复完成！")
    print("📋 修复摘要：")
    print("  ✅ 删除application.visa_type_id遗留字段")
    print("  ✅ automation_logs.task_status限制为3个最终状态")
    print("  ✅ applicant表护照信息集中排列")
    print("  ⏩ user表保持原有字段顺序不变")
    print("  ✅ application表按业务逻辑分组")
    print("  ✅ 已重组的表字段按 标识→核心业务→辅助信息→系统字段 排列")


def downgrade():
    print("⚠️ 此迁移的回滚操作可能导致数据丢失，请谨慎操作")

    # 恢复automation_logs的旧约束
    op.drop_constraint("automation_task_status_check", "automation_logs", type_="check")

    # 重新添加processing状态支持
    op.create_check_constraint(
        "check_completion_time_logic",
        "automation_logs",
        "(((task_status = 'processing' AND completed_at IS NULL) OR (task_status IN ('success', 'failed', 'cancelled') AND completed_at IS NOT NULL)))",
    )

    # 重新添加visa_type_id字段
    op.add_column("application", sa.Column("visa_type_id", sa.UUID(), nullable=True))

    print("⚠️ 回滚完成，但字段顺序不会恢复到原始状态")
