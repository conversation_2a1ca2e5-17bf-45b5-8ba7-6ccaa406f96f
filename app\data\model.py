# 文件: app/data/models.py (Improved Readability)
from dataclasses import dataclass


@dataclass
class VietnamEVisaApplicant:
    """Represents the data required for the Vietnam E-Visa application.
    Vietnam E-Visa专用申请人数据模型
    专门用于越南电子签证自动化填表引擎
    与数据库模型 app.data.models.applicant.Applicant 分离
    """

    # --- Personal Information ---
    customer_source: str | None = None  # customer source, for record only
    surname: str | None = None  # Applicant's surname
    given_name: str | None = None  # Applicant's given name(s)
    chinese_name: str | None = None  # Chinese name (only for UI display)
    sex: str | None = None  # e.g., "MALE", "FEMALE" (as extracted/input)
    dob: str | None = None  # Date of Birth, format: "DD/MM/YYYY"
    place_of_birth: str | None = (
        None  # Province/Place name (e.g., "GUANGDONG") - Used for Pinyin conversion
    )
    nationality: str | None = None  # e.g., "China"
    religion: str = "NO"  # Defaulted to "NO" as per filler logic

    # --- Passport Information ---
    passport_number: str | None = None
    passport_type: str = "Ordinary passport"  # Set fixed default value
    place_of_issue: str | None = None  # Province/Place name (e.g., "GUANGDONG")
    date_of_issue: str | None = None  # Date of Issue, format: "DD/MM/YYYY"
    passport_expiry: str | None = None  # Expiry Date, format: "DD/MM/YYYY"

    # --- Contact Information ---
    email: str | None = None
    telephone_number: str | None = (
        None  # Include country code if possible, e.g., "+8613xxxxxxxxx"
    )
    permanent_address: str | None = None  # Can be auto-generated if missing
    contact_address: str | None = None  # Can be auto-generated if missing

    # --- Emergency Contact ---
    emergency_contact_name: str | None = None  # Full name of emergency contact
    emergency_address: str | None = (
        None  # Can be auto-generated (uses contact_address) if missing
    )
    emergency_contact_phone: str | None = None  # Phone number of emergency contact
    # emergency_relationship is handled directly in filler ("Relative"), not stored here

    # --- Visa Request Details ---
    visa_entry_type: str | None = None  # Expected: "Single-entry" or "Multiple-entry"
    visa_validity_duration: str | None = None  # Expected: "30天" or "90天" (from UI)
    visa_start_date: str | None = (
        None  # Requested visa start date, format: "DD/MM/YYYY"
    )
    intended_entry_gate: str | None = (
        None  # Mapped English airport name from ENTRY_GATE_MAP
    )
    purpose_of_entry: str | None = (
        None  # Expected: "Tourist", "Business", "Visiting relatives", "Working", or "Other"
    )

    # --- Previous Visit Information ---
    visited_vietnam_last_year: bool | None = (
        None  # True if visited in the last year, False otherwise
    )
    previous_entry_date: str | None = (
        None  # Format: "DD/MM/YYYY" (Required if visited_vietnam_last_year is True)
    )
    previous_exit_date: str | None = (
        None  # Format: "DD/MM/YYYY" (Required if visited_vietnam_last_year is True)
    )
    previous_purpose: str | None = (
        None  # Expected English purpose, e.g., "Travel" (Required if visited_vietnam_last_year is True)
    )

    # --- Vietnam Contact Organization Information ---
    has_vietnam_contact: bool | None = (
        None  # True if has contact organization/individual in Vietnam, False otherwise
    )
    vietnam_contact_organization: str | None = (
        None  # Name of hosting organization (Required if has_vietnam_contact is True)
    )
    vietnam_contact_phone: str | None = (
        None  # Telephone number of contact (Required if has_vietnam_contact is True)
    )
    vietnam_contact_address: str | None = (
        None  # Address of contact (Required if has_vietnam_contact is True)
    )
    vietnam_contact_purpose: str | None = (
        None  # Purpose of contact (Required if has_vietnam_contact is True)
    )

    # --- File Paths ---
    portrait_photo_path: str | None = None  # Absolute path to the portrait photo file
    passport_scan_path: str | None = None  # Absolute path to the passport scan file
