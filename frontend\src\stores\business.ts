/**
 * 统一业务状态管理Store
 * =====================
 *
 * 合并原有的applicationStore和submissionStore，
 * 统一管理订单、申请、提交状态和轮询逻辑
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api/request'

// 提交状态枚举
export enum SubmissionState {
  IDLE = 'idle',
  SUBMITTING = 'submitting',
  SUCCESS = 'success',
  FAILED = 'failed',
}

// 应用项目接口（合并原有接口）
export interface BusinessItem {
  // 基础信息
  passportNumber: string
  applicantName: string
  chineseName?: string
  submissionTime: string

  // 订单状态
  orderStatus: 'created' | 'cancelled'
  orderNo?: string

  // 自动化任务状态
  automationStatus: 'processing' | 'success' | 'failed' | 'cancelled'
  automationMessage?: string

  // 签证流程状态
  visaStatus?: 'submit_failure' | 'submitted' | 'additional_info_required' | 'approved' | 'denied'
  visaMessage?: string
  applicationId?: string
}

// 轮询配置
interface PollingConfig {
  interval: number
  maxRetries: number
  backoffMultiplier: number
}

export const useBusinessStore = defineStore('business', () => {
  // ===== 提交状态管理 =====
  const submissionState = ref<SubmissionState>(SubmissionState.IDLE)
  const currentSubmittingPassport = ref<string | null>(null)
  const recentSubmissions = ref<Map<string, number>>(new Map())

  // ===== 业务数据管理 =====
  const businessItems = ref<BusinessItem[]>([])

  // ===== 轮询状态管理 =====
  const pollingTimer = ref<number | null>(null)
  const isPollingActive = ref(false)
  const pollingConfig = ref<PollingConfig>({
    interval: 5000, // 5秒
    maxRetries: 3,
    backoffMultiplier: 1.5,
  })

  // ===== 计算属性 =====

  // 提交状态相关
  const isSubmitting = computed(() => submissionState.value === SubmissionState.SUBMITTING)
  const isIdle = computed(() => submissionState.value === SubmissionState.IDLE)

  // 业务数据相关
  const hasBusinessItems = computed(() => businessItems.value.length > 0)

  const businessStats = computed(() => ({
    total: businessItems.value.length,
    processing: businessItems.value.filter((item) => item.automationStatus === 'processing').length,
    success: businessItems.value.filter((item) => item.automationStatus === 'success').length,
    failed: businessItems.value.filter((item) => item.automationStatus === 'failed').length,
    cancelled: businessItems.value.filter((item) => item.automationStatus === 'cancelled').length,
  }))

  const sortedBusinessItems = computed(() => {
    return [...businessItems.value].sort(
      (a, b) => new Date(b.submissionTime).getTime() - new Date(a.submissionTime).getTime(),
    )
  })

  // 需要轮询的项目（处理中状态）
  const itemsNeedingPolling = computed(() =>
    businessItems.value.filter((item) => item.automationStatus === 'processing'),
  )

  // ===== 提交管理方法 =====

  function isQuickDuplicateSubmission(passportNumber: string): boolean {
    cleanupRecentSubmissions()
    const lastSubmission = recentSubmissions.value.get(passportNumber)
    if (!lastSubmission) return false

    const timeDiff = Date.now() - lastSubmission
    return timeDiff < 60 * 1000 // 1分钟内算重复
  }

  function startSubmission(passportNumber: string) {
    if (isSubmitting.value) {
      throw new Error('申请正在提交中，请勿重复点击提交按钮。')
    }

    submissionState.value = SubmissionState.SUBMITTING
    currentSubmittingPassport.value = passportNumber
    console.log('🚀 开始提交流程:', passportNumber)
  }

  function markSubmissionSuccess(passportNumber: string) {
    submissionState.value = SubmissionState.SUCCESS
    recentSubmissions.value.set(passportNumber, Date.now())

    console.log('✅ 提交成功，记录时间戳:', passportNumber)

    // 延迟重置状态
    setTimeout(() => {
      resetSubmissionState()
    }, 500)
  }

  function markSubmissionFailed(passportNumber: string, error?: string) {
    submissionState.value = SubmissionState.FAILED
    console.log('❌ 提交失败:', passportNumber, error)

    // 延迟重置状态
    setTimeout(() => {
      resetSubmissionState()
    }, 2000)
  }

  function resetSubmissionState() {
    submissionState.value = SubmissionState.IDLE
    currentSubmittingPassport.value = null
    console.log('🔄 提交状态已重置')
  }

  function cleanupRecentSubmissions() {
    const now = Date.now()
    const cutoff = 60 * 1000 // 1分钟

    for (const [passport, timestamp] of recentSubmissions.value.entries()) {
      if (now - timestamp > cutoff) {
        recentSubmissions.value.delete(passport)
      }
    }
  }

  // ===== 业务数据管理方法 =====

  function addBusinessItem(item: BusinessItem) {
    const normalizedItem: BusinessItem = {
      passportNumber: item.passportNumber,
      applicantName: item.applicantName,
      chineseName: item.chineseName,
      submissionTime: item.submissionTime,
      orderStatus: item.orderStatus || 'created',
      orderNo: item.orderNo,
      automationStatus: item.automationStatus || 'processing',
      automationMessage: item.automationMessage,
      visaStatus: item.visaStatus,
      visaMessage: item.visaMessage,
      applicationId: item.applicationId,
    }

    const existingIndex = businessItems.value.findIndex(
      (existing) => existing.passportNumber === normalizedItem.passportNumber,
    )

    if (existingIndex >= 0) {
      // 更新现有项目
      businessItems.value[existingIndex] = {
        ...businessItems.value[existingIndex],
        ...normalizedItem,
      }
      console.log(`✅ 更新业务项目: ${normalizedItem.passportNumber}`)
    } else {
      // 添加新项目
      businessItems.value.unshift(normalizedItem)
      console.log(`✅ 添加新业务项目: ${normalizedItem.passportNumber}`)
    }

    saveToStorage()

    // 如果有处理中的项目，启动轮询
    if (itemsNeedingPolling.value.length > 0 && !isPollingActive.value) {
      startPolling()
    }
  }

  function updateBusinessItemStatus(
    passportNumber: string,
    automationStatus: BusinessItem['automationStatus'],
    additionalData?: Partial<BusinessItem>,
  ) {
    const item = businessItems.value.find((item) => item.passportNumber === passportNumber)
    if (item) {
      item.automationStatus = automationStatus

      if (additionalData) {
        Object.assign(item, additionalData)
      }

      console.log(`✅ 业务项目状态已更新: ${passportNumber} -> ${automationStatus}`)
      saveToStorage()

      // 如果没有处理中的项目了，停止轮询
      if (itemsNeedingPolling.value.length === 0) {
        stopPolling()
      }
    } else {
      console.warn(`⚠️ 未找到护照号为 ${passportNumber} 的业务项目`)
    }
  }

  function updateBusinessItemByOrderNo(
    orderNo: string,
    automationStatus: BusinessItem['automationStatus'],
    additionalData?: Partial<BusinessItem>,
  ) {
    const item = businessItems.value.find((item) => item.orderNo === orderNo)
    if (item) {
      item.automationStatus = automationStatus

      if (additionalData) {
        Object.assign(item, additionalData)
      }

      console.log(`✅ 业务项目状态已更新 (按订单号): ${orderNo} -> ${automationStatus}`)
      saveToStorage()

      if (itemsNeedingPolling.value.length === 0) {
        stopPolling()
      }
    } else {
      console.warn(`⚠️ 未找到订单号为 ${orderNo} 的业务项目`)
    }
  }

  function getBusinessItemsByStatus(automationStatus: BusinessItem['automationStatus']) {
    return businessItems.value.filter((item) => item.automationStatus === automationStatus)
  }

  function clearBusinessItems() {
    businessItems.value = []
    saveToStorage()
    stopPolling()
    console.log('✅ 业务项目列表已清空')
  }

  // ===== 数据持久化 =====

  function saveToStorage() {
    try {
      localStorage.setItem('business_items', JSON.stringify(businessItems.value))
    } catch (error) {
      console.error('❌ 保存到本地存储失败:', error)
    }
  }

  function loadFromStorage() {
    try {
      const stored = localStorage.getItem('business_items')
      if (stored) {
        businessItems.value = JSON.parse(stored)
        console.log('✅ 从本地存储加载业务项目:', businessItems.value.length)

        // 如果有处理中的项目，启动轮询
        if (itemsNeedingPolling.value.length > 0) {
          startPolling()
        }
      }
    } catch (error) {
      console.error('❌ 从本地存储加载失败:', error)
      businessItems.value = []
    }
  }

  // ===== 统一轮询逻辑 =====

  async function startPolling() {
    if (isPollingActive.value) {
      console.log('⚠️ 轮询已在运行中')
      return
    }

    isPollingActive.value = true
    console.log('🔄 启动统一轮询')

    const poll = async () => {
      if (!isPollingActive.value || itemsNeedingPolling.value.length === 0) {
        stopPolling()
        return
      }

      try {
        await pollBusinessItemsStatus()
      } catch (error) {
        console.error('❌ 轮询出错:', error)
      }

      if (isPollingActive.value) {
        pollingTimer.value = window.setTimeout(poll, pollingConfig.value.interval)
      }
    }

    // 立即执行一次，然后开始定时轮询
    poll()
  }

  function stopPolling() {
    if (pollingTimer.value) {
      clearTimeout(pollingTimer.value)
      pollingTimer.value = null
    }
    isPollingActive.value = false
    console.log('⏹️ 统一轮询已停止')
  }

  async function pollBusinessItemsStatus() {
    const processingItems = itemsNeedingPolling.value
    if (processingItems.length === 0) return

    console.log(`🔍 轮询 ${processingItems.length} 个处理中的项目状态`)

    for (const item of processingItems) {
      if (!item.orderNo) continue

      try {
        const response = await api.get(`/api/automation-logs/status/${item.orderNo}`)

        if (response.success && response.data) {
          const data = response.data as {
            task_status?: string
            error_message?: string
            vietnam_application_number?: string
          }
          const { task_status, error_message, vietnam_application_number } = data

          if (task_status !== 'processing') {
            updateBusinessItemStatus(
              item.passportNumber,
              task_status as BusinessItem['automationStatus'],
              {
                automationMessage: error_message,
                applicationId: vietnam_application_number,
              },
            )
          }
        }
      } catch (error) {
        console.error(`❌ 轮询项目 ${item.orderNo} 状态失败:`, error)
      }
    }
  }

  return {
    // 状态
    submissionState,
    currentSubmittingPassport,
    businessItems,
    isPollingActive,

    // 计算属性
    isSubmitting,
    isIdle,
    hasBusinessItems,
    businessStats,
    sortedBusinessItems,
    itemsNeedingPolling,

    // 提交管理
    isQuickDuplicateSubmission,
    startSubmission,
    markSubmissionSuccess,
    markSubmissionFailed,
    resetSubmissionState,
    cleanupRecentSubmissions,

    // 业务数据管理
    addBusinessItem,
    updateBusinessItemStatus,
    updateBusinessItemByOrderNo,
    getBusinessItemsByStatus,
    clearBusinessItems,

    // 数据持久化
    saveToStorage,
    loadFromStorage,

    // 轮询管理
    startPolling,
    stopPolling,
    pollBusinessItemsStatus,
  }
})
