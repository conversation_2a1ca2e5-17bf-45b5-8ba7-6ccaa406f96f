# 文件: app/core/user_prefs.py
# 职责：提供加载和保存用户偏好设置（如上次输入的表单数据）的功能。
#      使用 JSON 文件进行简单的本地持久化存储。

import json
from pathlib import Path
from typing import Any  # 用于类型提示

from app.utils.logger_config import get_logger

# 获取日志记录器实例
logger = get_logger()

# 定义偏好设置文件的存储位置
# 使用 Path 对象处理路径，更可靠
# BASE_DIR 指向项目根目录 (visa_automator)
BASE_DIR = Path(__file__).resolve().parent.parent.parent
# 将偏好文件存储在项目根目录下的 .prefs 隐藏文件夹中
PREFS_DIR = BASE_DIR / ".prefs"
PREFS_FILE = PREFS_DIR / "user_prefs.json"


def _ensure_prefs_dir():
    """确保存储偏好设置的目录存在"""
    try:
        PREFS_DIR.mkdir(
            parents=True, exist_ok=True
        )  # parents=True 允许创建多级目录，exist_ok=True 如果目录已存在则不报错
    except OSError as e:
        logger.error(f"创建偏好设置目录失败: {PREFS_DIR} - {e}", exc_info=True)
        # 如果目录创建失败，后续保存操作可能会失败


def _load_prefs() -> dict:
    """
    从 JSON 文件加载偏好设置。
    如果文件不存在、为空或格式错误，则返回一个空字典。
    """
    _ensure_prefs_dir()  # 确保目录存在再尝试读取

    if not PREFS_FILE.is_file():  # 使用 is_file() 检查文件是否存在且是一个文件
        logger.info(f"偏好设置文件不存在: {PREFS_FILE}. 将返回空设置。")
        return {}

    try:
        with open(PREFS_FILE, encoding="utf-8") as f:
            content = f.read().strip()  # 读取并去除首尾空白
            if not content:  # 处理空文件的情况
                logger.warning(f"偏好设置文件为空: {PREFS_FILE}. 将返回空设置。")
                return {}
            # 解析 JSON 内容
            prefs = json.loads(content)  # 使用 loads 而不是 load，因为我们先读取了内容
            # 验证加载的数据是否为字典类型
            if not isinstance(prefs, dict):
                logger.warning(
                    f"偏好设置文件 {PREFS_FILE} 顶层结构不是字典，格式错误。已重置为空设置。"
                )
                # (可选) 可以考虑备份错误的文件
                # PREFS_FILE.rename(PREFS_FILE.with_suffix('.json.bak'))
                return {}
            logger.info(f"成功从 {PREFS_FILE} 加载偏好设置。")
            return prefs
    except json.JSONDecodeError:
        logger.warning(
            f"解析偏好设置文件 {PREFS_FILE} 失败，可能文件已损坏。已重置为空设置。"
        )
        # (可选) 备份损坏的文件
        # PREFS_FILE.rename(PREFS_FILE.with_suffix('.json.error'))
        return {}
    except Exception as e:
        logger.error(f"加载偏好设置时发生未知错误: {e}", exc_info=True)
        return {}


def _save_prefs(prefs: dict) -> bool:
    """
    将偏好设置字典保存到 JSON 文件。
    返回 True 表示保存成功，False 表示失败。
    """
    _ensure_prefs_dir()  # 确保目录存在再尝试写入

    try:
        with open(PREFS_FILE, "w", encoding="utf-8") as f:
            # 将字典序列化为 JSON 字符串并写入文件
            # indent=4 使 JSON 文件格式化，更易于手动阅读和编辑
            # ensure_ascii=False 允许直接写入非 ASCII 字符（例如中文）
            json.dump(prefs, f, ensure_ascii=False, indent=4)
        logger.info(f"偏好设置已成功保存到: {PREFS_FILE}")
        return True
    except TypeError as e:  # 捕获 JSON 序列化错误
        logger.error(f"无法序列化偏好设置数据为 JSON: {e}", exc_info=True)
        logger.error(f"问题数据: {prefs}")  # 记录导致问题的数据
        return False
    except Exception as e:
        logger.error(f"保存偏好设置到 {PREFS_FILE} 时发生未知错误: {e}", exc_info=True)
        return False


# --- 公开使用的函数 ---


def save_preference(key: str, value: Any):
    """
    保存单个偏好设置项 (键值对)。
    value 可以是任何可 JSON 序列化的类型 (字符串, 数字, 列表, 字典等)。
    """
    if not isinstance(key, str) or not key:
        logger.error("保存偏好设置失败：key 必须是非空字符串。")
        return False
    logger.info(f"准备保存偏好设置: key='{key}'")  # 使用 debug 级别记录细节
    prefs = _load_prefs()  # 先加载当前的设置
    prefs[key] = value  # 更新或添加指定的键值对
    return _save_prefs(prefs)  # 保存修改后的完整设置


def load_preference(key: str, default: Any = None) -> Any:
    """
    加载单个偏好设置项。
    如果指定的 key 不存在，则返回提供的 default 值 (默认为 None)。
    """
    if not isinstance(key, str) or not key:
        logger.error("加载偏好设置失败：key 必须是非空字符串。")
        return default
    # logger.debug(f"准备加载偏好设置: key='{key}'")
    prefs = _load_prefs()  # 加载所有设置
    # 使用字典的 get 方法安全地获取值，如果 key 不存在则返回 default
    value = prefs.get(key, default)
    # logger.debug(f"加载偏好 key='{key}', value='{value}' (类型: {type(value)})") # 可选：记录加载的值和类型
    return value


def load_all_preferences() -> dict:
    """加载所有偏好设置，返回整个字典"""
    logger.info("加载所有偏好设置...")
    return _load_prefs()


def save_all_preferences(prefs_dict: dict) -> bool:
    """保存整个偏好设置字典（会覆盖旧文件）"""
    if not isinstance(prefs_dict, dict):
        logger.error("保存所有偏好设置失败：提供的数据必须是字典。")
        return False
    logger.info("准备保存所有偏好设置...")
    return _save_prefs(prefs_dict)


# --- (可选) 清除设置的功能 ---
def clear_preference(key: str) -> bool:
    """删除指定的偏好设置项"""
    if not isinstance(key, str) or not key:
        logger.error("清除偏好设置失败：key 必须是非空字符串。")
        return False
    logger.info(f"准备清除偏好设置: key='{key}'")
    prefs = _load_prefs()
    if key in prefs:
        del prefs[key]
        logger.info(f"偏好设置项 '{key}' 已被清除。")
        return _save_prefs(prefs)
    else:
        logger.warning(f"尝试清除不存在的偏好设置项: '{key}'")
        return True  # 或者 False，取决于业务逻辑


def clear_all_preferences() -> bool:
    """清除所有偏好设置"""
    logger.warning("!!! 正在清除所有偏好设置 !!!")
    return _save_prefs({})  # 保存一个空字典即可
