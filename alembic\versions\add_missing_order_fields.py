"""重构Order表 - 专注订单和支付管理，避免字段冗余

Revision ID: add_missing_order_fields
Revises: add_missing_company_fields
Create Date: 2025-01-23 12:00:00.000000

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "add_missing_order_fields"
down_revision = "add_missing_company_fields"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """重构Order表 - 移除业务逻辑字段，专注订单管理"""

    # 🗑️ 第一步：移除不合适的字段
    op.drop_column("order", "status")  # 移除业务状态（由application.status管理）
    op.drop_column("order", "total_amount")  # 移除总金额（由user_payment.amount管理）

    # 📋 第二步：添加订单级别管理字段（不与user_payment冲突）
    op.add_column(
        "order",
        sa.Column(
            "order_status",
            sa.String(32),
            nullable=False,
            server_default="pending",
            comment="订单状态: pending, confirmed, cancelled",
        ),
    )
    op.add_column(
        "order",
        sa.Column(
            "order_type",
            sa.String(32),
            nullable=False,
            server_default="visa_application",
            comment="订单类型",
        ),
    )

    # 🔧 第三步：添加技术控制字段
    op.add_column(
        "order",
        sa.Column(
            "idempotent_key",
            sa.String(100),
            nullable=True,
            comment="幂等性键，防止重复创建订单",
        ),
    )
    op.add_column(
        "order", sa.Column("notes", sa.Text(), nullable=True, comment="订单备注信息")
    )

    # 📊 第四步：添加索引优化查询性能
    op.create_index("ix_order_status", "order", ["order_status"])
    op.create_index("ix_order_idempotent_key", "order", ["idempotent_key"], unique=True)
    op.create_index("ix_order_user_status", "order", ["user_id", "order_status"])


def downgrade() -> None:
    """回滚Order表重构"""

    # 删除新增的索引
    op.drop_index("ix_order_user_status")
    op.drop_index("ix_order_idempotent_key")
    op.drop_index("ix_order_status")

    # 删除新增的字段
    op.drop_column("order", "notes")
    op.drop_column("order", "idempotent_key")
    op.drop_column("order", "order_type")
    op.drop_column("order", "order_status")

    # 恢复原有字段
    op.add_column("order", sa.Column("total_amount", sa.Float(), nullable=True))
    op.add_column("order", sa.Column("status", sa.String(32), nullable=False))
