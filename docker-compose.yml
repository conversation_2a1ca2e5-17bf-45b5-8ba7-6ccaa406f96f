services:
  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: visa_automator_postgres
    environment:
      POSTGRES_DB: visa_automator
      POSTGRES_USER: visa_user
      POSTGRES_PASSWORD: visa_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      TZ: Asia/Shanghai
      PGTZ: Asia/Shanghai  # PostgreSQL特定时区设置
    command: >
      postgres
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_connections=200
      -c timezone='Asia/Shanghai'
      -c log_timezone='Asia/Shanghai'
      -c log_line_prefix='%t [%p] %q%u@%d '
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro

    ports:
      - "5432:5432"
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U visa_user -d visa_automator"]
      interval: 120s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  visa-automator:
    build: .
    volumes:
      - ./:/app
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    ports:
      - "8000:8000"
    env_file:
      - ./.env
    environment:
      - REDIS_HOST=redis
      - TZ=Asia/Shanghai
    shm_size: 8gb
    restart: unless-stopped
    command: ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    expose:
      - "8000"
    networks:
      - app-network
    healthcheck:
      test: >
        ["CMD", "python", "-c",
         "import urllib.request; urllib.request.urlopen('http://localhost:8000/api/visa/health')"]
      interval: 120s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

  redis:
    image: redis:7-alpine
    container_name: visa_automator_redis
    command: >
      redis-server
      --save 60 1
      --loglevel warning
      --maxclients 1000
      --tcp-keepalive 60
      --tcp-backlog 511
      --timeout 300
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 60s
      timeout: 5s
      retries: 3
    restart: unless-stopped

  # 🔥 独立邮件轮询服务
  # 专门负责邮件轮询，与Celery Worker分离，避免重复轮询
  email-polling:
    build:
      context: .
      dockerfile: Dockerfile.email
    container_name: visa_automator_email_polling
    volumes:
      - ./:/app
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    env_file:
      - ./.env
    environment:
      - TZ=Asia/Shanghai
    networks:
      - app-network
    depends_on:
      visa-automator:
        condition: service_healthy
    restart: unless-stopped
    # 🔥 资源限制（邮件轮询 + 签证下载需要更多资源）
    # 由于集成了Playwright浏览器自动化，需要增加资源配置
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    healthcheck:
      test: >
        ["CMD", "python", "-c",
         "import requests; import sys; r = requests.get('http://localhost:8001/health', timeout=5);
          sys.exit(0 if r.status_code == 200 else 1)"]
      interval: 120s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.celery
    #container_name: visa_automator_celery_worker  remove the name, it will be auto generated
    volumes:
      - ./:/app
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    env_file:
      - ./.env
    environment:
      - REDIS_HOST=redis
      - TZ=Asia/Shanghai
    networks:
      - app-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

# 修正：在开发环境中，我们不需要Nginx。
# 我们将直接访问Vite开发服务器(端口5173)，并使用其代理功能。
# 这可以确保前端代码的热重载正常工作。
# 因此，我们将整个nginx-lb服务注释掉。
# nginx-lb:
#   image: nginx:latest
#   container_name: visa_automator_nginx_lb
#   ports:
#     - "8000:8000"
#   volumes:
#     - ./nginx.conf:/etc/nginx/nginx.conf:ro
#     - ./frontend/dist:/usr/share/nginx/html:ro
#   depends_on:
#     - visa-automator
#   restart: unless-stopped
#   networks:
#     - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# 使用 scale 命令来启动多个 visa-automator 实例：
# docker-compose up -d --scale visa-automator=3 nginx-lb
# (这将启动 3 个 visa-automator 实例和 1 个 nginx-lb 实例)
# 或者只启动并扩展服务，Nginx 单独定义：
# docker-compose up -d --scale visa-automator=3
# docker-compose up -d nginx-lb # 需要确保 nginx-lb 依赖的 visa-automator 已启动

# 推荐的扩展配置：
# 开发环境：docker-compose up -d --scale visa-automator=2
# 生产环境：docker-compose up -d --scale visa-automator=5
# 高负载环境：docker-compose up -d --scale visa-automator=10
