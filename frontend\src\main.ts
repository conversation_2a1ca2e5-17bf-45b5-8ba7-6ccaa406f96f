import './assets/main.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

// 🔥 新增：导入存储清理功能
import { useStorageCleanup } from '@/composables/useStorageCleanup'

const app = createApp(App)
const pinia = createPinia()

// 配置Pinia持久化插件
pinia.use(piniaPluginPersistedstate)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 🔥 新增：应用启动时初始化存储清理
const initializeStorageCleanup = async () => {
  try {
    const { initAutoCleanup } = useStorageCleanup()
    await initAutoCleanup()
    console.log('✅ 存储清理系统初始化完成')
  } catch (error) {
    console.warn('⚠️ 存储清理系统初始化失败:', error)
  }
}

// 在应用挂载前初始化清理系统
initializeStorageCleanup()

// 挂载应用
app.mount('#app')

// 🔧 遵循 Pinia 最佳实践：应用启动后异步初始化 stores
// 使用 nextTick 确保应用完全初始化后再使用 store
import { nextTick } from 'vue'

nextTick(() => {
  // 动态导入并初始化 stores，确保 Pinia 已完全注册
  Promise.all([import('@/stores/session'), import('@/stores/application')])
    .then(([sessionModule, applicationModule]) => {
      const sessionStore = sessionModule.useSessionStore()
      const applicationStore = applicationModule.useApplicationStore()

      // 🔧 修改：清空历史记录，只显示本次登录的提交记录
      applicationStore.clearApplications() // 清空所有历史记录
      console.log('[App] 已清空历史记录，只显示本次登录记录')

      // 延迟启动会话监控
      setTimeout(() => {
        console.log('[App] 启动会话监控（跳过初始检查）')
        sessionStore.startSessionMonitoring()
      }, 5000)

      console.log('[App] 所有 stores 初始化完成')
    })
    .catch((error) => {
      console.error('[App] Stores 初始化失败:', error)
    })
})
