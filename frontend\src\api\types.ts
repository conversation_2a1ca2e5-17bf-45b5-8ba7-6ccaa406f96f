// API响应基础接口
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 会话检查响应
export interface SessionCheckResponse {
  valid: boolean
}

// 重复检查响应，严格对应旧版接口
export interface DuplicateCheckResponse {
  success: boolean
  exists: boolean
  can_resubmit?: boolean // 是否允许重新提交
  warning_type?: 'success' | 'pending' | 'rejected' | 'failed' | 'none' | 'error' // 警告类型
  application_number?: string
  submission_time?: string
  status?: string
  approval_status?: string
  order_no?: string
  error?: string
}

// 签证申请响应类型
export interface VisaApplicationResponse {
  success: boolean
  message: string
  application_id?: string
  status: string
  tracking_info?: {
    submission_time: string
    expected_processing_days: string
    email?: string
    order_no?: string
    vietnam_application_number?: string
    is_duplicate?: boolean
    failure_reason?: string
  }
}

// OCR识别响应，严格对应旧版接口
export interface OcrResponse {
  success: boolean
  data?: {
    surname?: string
    given_name?: string
    chinese_name?: string
    passport_number?: string
    date_of_birth?: string
    place_of_birth?: string
    nationality?: string
    sex?: string
    date_of_issue?: string
    date_of_expiry?: string
    place_of_issue?: string
    [key: string]: unknown
  }
  message?: string
  error?: string
}

// ==================== 订单管理相关类型 ====================

// 订单状态枚举 - 根据修正后的业务流程
export type OrderStatus =
  | 'created' // 订单已创建
  | 'cancelled' // 订单已取消
  | 'processing' // 提交中
  | 'success' // 提交成功（自动化填表成功，获得越南官方编号）
  | 'pending_approve' // 等待审批（success之后的状态）
  | 'approved' // 已审批
  | 'pending_download' // 等待下载
  | 'downloaded' // 已下载
  | 'paid' // 已付款（付款成功）
  | 'failed' // 失败
  | 'unknown' // 未知状态
  | 'reserved1' // 扩展状态1
  | 'reserved2' // 扩展状态2
  | 'reserved3' // 扩展状态3

// 订单信息接口
export interface OrderInfo {
  // 核心编号
  order_no: string // 我们的订单编号
  application_number?: string // 越南官方编号

  // 基本信息
  user_id: number
  applicant_name: string
  passport_number: string
  date_of_birth: string

  // 状态信息
  status: OrderStatus
  error_message?: string
  last_error_at?: string

  // 重试信息
  retry_count: number
  max_retry_count: number

  // 业务数据
  application_data: Record<string, unknown>

  // 时间信息
  created_at: string
  updated_at: string
  submitted_at?: string
  approved_at?: string
  completed_at?: string
}

// 订单状态历史
export interface OrderStatusHistory {
  id: number
  order_no: string
  from_status?: string
  to_status: string
  reason?: string
  operator_id?: number
  operator_type: 'system' | 'admin' | 'user'
  metadata?: Record<string, unknown>
  created_at: string
}

// 创建订单请求
export interface CreateOrderRequest {
  // 幂等性标识数据
  applicant_name: string
  passport_number: string
  date_of_birth: string

  // 完整申请数据
  application_data: Record<string, unknown>
}

// 创建订单响应
export interface CreateOrderResponse {
  success: boolean
  data: {
    order_no: string
    status: OrderStatus
    created_at: string
  }
  message?: string
}

// 订单查询响应
export interface OrderQueryResponse {
  success: boolean
  data: {
    orders: OrderInfo[]
    pagination: PaginationInfo
  }
  message?: string
}

// 订单详情响应
export interface OrderDetailResponse {
  success: boolean
  data: {
    order: OrderInfo
    status_history: OrderStatusHistory[]
  }
  message?: string
}

// 订单更新请求
export interface UpdateOrderRequest {
  application_number?: string // 更新越南官方编号
  status?: OrderStatus // 更新状态
  error_message?: string // 错误信息
  metadata?: Record<string, unknown> // 额外数据
}

// 分页信息
export interface PaginationInfo {
  current_page: number
  total_pages: number
  total_items: number
  has_prev: boolean
  has_next: boolean
  page_size: number
}

// API错误接口
export interface ApiError {
  status: number
  message: string
  details?: unknown
}
