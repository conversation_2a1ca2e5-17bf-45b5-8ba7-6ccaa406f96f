"""
申请Repository - 申请数据访问层
===============================

负责申请相关的所有数据库操作，隔离业务逻辑与数据访问技术。
支持Session注入，符合依赖注入原则。
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Any
from uuid import UUID

from sqlalchemy import desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.data.models.applicant import Applicant
from app.data.models.application import Application
from app.data.models.order import Order

from .base import SQLAlchemyRepository


class ApplicationRepository(SQLAlchemyRepository[Application, UUID]):
    """申请Repository - 专门处理申请数据访问"""

    def __init__(self, session: AsyncSession):
        """
        接受Session注入
        """
        super().__init__(session, Application)

    async def get_by_order_id(self, order_id: UUID) -> Application | None:
        """根据订单ID获取申请"""
        result = await self.session.execute(
            select(Application)
            .options(selectinload(Application.applicant))
            .where(Application.order_id == order_id)
        )
        return result.scalar_one_or_none()

    async def get_by_vietnam_application_number(
        self, vietnam_app_number: str
    ) -> Application | None:
        """根据越南申请编号获取申请"""
        result = await self.session.execute(
            select(Application).where(
                Application.vietnam_application_number == vietnam_app_number
            )
        )
        return result.scalar_one_or_none()

    async def get_applications_by_user_id(
        self,
        user_id: UUID,
        status: str | None = None,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Application]:
        """获取用户的申请列表（通过订单关联）"""
        query = (
            select(Application)
            .join(Order, Application.order_id == Order.id)
            .options(selectinload(Application.applicant))
            .where(Order.user_id == user_id)
        )

        if status:
            query = query.where(Application.status == status)

        query = query.order_by(desc(Application.created_at)).limit(limit).offset(offset)

        result = await self.session.execute(query)
        return result.scalars().all()

    async def count_applications_by_user_id(
        self, user_id: UUID, status: str | None = None
    ) -> int:
        """统计用户申请数量"""
        query = (
            select(func.count(Application.id))
            .join(Order, Application.order_id == Order.id)
            .where(Order.user_id == user_id)
        )

        if status:
            query = query.where(Application.status == status)

        result = await self.session.execute(query)
        return result.scalar()

    async def get_applications_by_status(
        self, status: str, limit: int = 100, offset: int = 0
    ) -> list[Application]:
        """根据状态获取申请列表"""
        result = await self.session.execute(
            select(Application)
            .options(selectinload(Application.applicant))
            .where(Application.status == status)
            .order_by(desc(Application.created_at))
            .limit(limit)
            .offset(offset)
        )
        return result.scalars().all()

    async def get_recent_applications(
        self, hours: int = 24, limit: int = 50
    ) -> list[Application]:
        """获取最近的申请"""
        since = datetime.utcnow() - timedelta(hours=hours)
        result = await self.session.execute(
            select(Application)
            .options(selectinload(Application.applicant))
            .where(Application.created_at >= since)
            .order_by(desc(Application.created_at))
            .limit(limit)
        )
        return result.scalars().all()

    async def update_status(
        self,
        application_id: UUID,
        new_status: str,
        vietnam_application_number: str | None = None,
    ) -> bool:
        """更新申请状态"""
        update_data = {"status": new_status, "updated_at": datetime.utcnow()}

        if vietnam_application_number:
            update_data["vietnam_application_number"] = vietnam_application_number

        result = await self.session.execute(
            update(Application)
            .where(Application.id == application_id)
            .values(**update_data)
        )

        await self.session.commit()
        return result.rowcount > 0

    async def get_applications_with_order_info(
        self, user_id: UUID | None = None, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """获取包含订单信息的申请列表"""
        query = (
            select(
                Application.id,
                Application.status,
                Application.vietnam_application_number,
                Application.created_at,
                Application.updated_at,
                Order.order_no,
                Order.order_status,
                Applicant.passport_number,
                Applicant.surname,
                Applicant.given_name,
                Applicant.chinese_name,
            )
            .join(Order, Application.order_id == Order.id)
            .join(Applicant, Application.applicant_id == Applicant.id)
        )

        if user_id:
            query = query.where(Order.user_id == user_id)

        query = query.order_by(desc(Application.created_at)).limit(limit).offset(offset)

        result = await self.session.execute(query)
        rows = result.fetchall()

        return [
            {
                "id": row.id,
                "status": row.status,
                "vietnam_application_number": row.vietnam_application_number,
                "created_at": row.created_at,
                "updated_at": row.updated_at,
                "order_no": row.order_no,
                "order_status": row.order_status,
                "passport_number": row.passport_number,
                "surname": row.surname,
                "given_name": row.given_name,
                "chinese_name": row.chinese_name,
                "applicant_name": f"{row.surname} {row.given_name}".strip(),
            }
            for row in rows
        ]

    async def get_application_statistics(
        self, user_id: UUID | None = None
    ) -> dict[str, int]:
        """获取申请统计信息"""
        query = select(Application.status, func.count(Application.id))

        if user_id:
            query = query.join(Order, Application.order_id == Order.id).where(
                Order.user_id == user_id
            )

        query = query.group_by(Application.status)

        result = await self.session.execute(query)
        stats = dict(result.fetchall())

        # 确保所有状态都有值
        all_statuses = [
            "pending",
            "processing",
            "submitted",
            "approved",
            "denied",
            "cancelled",
        ]
        return {status: stats.get(status, 0) for status in all_statuses}
