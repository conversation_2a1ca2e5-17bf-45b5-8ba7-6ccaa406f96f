"""创建automation_logs表 - 任务执行跟踪

Revision ID: create_automation_logs_table
Revises: add_missing_order_fields
Create Date: 2025-01-23 13:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "create_automation_logs_table"
down_revision = "add_missing_order_fields"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """创建automation_logs表 - 任务执行跟踪和日志记录"""

    op.create_table(
        "automation_logs",
        # 🔑 主键和关联
        sa.Column("id", UUID(as_uuid=True), primary_key=True, comment="日志主键"),
        sa.Column(
            "application_id",
            UUID(as_uuid=True),
            sa.ForeignKey("application.id", ondelete="CASCADE"),
            nullable=False,
            comment="关联申请",
        ),
        sa.Column(
            "order_id",
            UUID(as_uuid=True),
            sa.<PERSON>ey("order.id", ondelete="CASCADE"),
            nullable=False,
            comment="关联订单",
        ),
        # 🎯 任务执行信息
        sa.Column(
            "task_type",
            sa.String(32),
            nullable=False,
            comment="任务类型: vietnam_evisa, passport_ocr",
        ),
        sa.Column(
            "task_status",
            sa.String(16),
            nullable=False,
            comment="任务状态: processing, success, failed, cancelled",
        ),
        sa.Column("error_message", sa.Text(), nullable=True, comment="错误详细信息"),
        sa.Column("retry_count", sa.Integer(), default=0, comment="重试次数"),
        sa.Column("max_retries", sa.Integer(), default=3, comment="最大重试次数"),
        # 🔧 Celery集成
        sa.Column(
            "celery_task_id", sa.String(64), nullable=True, comment="Celery任务ID"
        ),
        # ⏰ 时间戳
        sa.Column("started_at", sa.DateTime(), nullable=True, comment="任务开始时间"),
        sa.Column("completed_at", sa.DateTime(), nullable=True, comment="任务完成时间"),
        sa.Column(
            "created_at",
            sa.DateTime(),
            server_default=sa.text("now()"),
            comment="记录创建时间",
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(),
            server_default=sa.text("now()"),
            comment="记录更新时间",
        ),
    )

    # 📊 创建索引优化查询性能 (只创建不会自动生成的索引)
    op.create_index(
        "ix_automation_logs_application_id", "automation_logs", ["application_id"]
    )
    op.create_index("ix_automation_logs_order_id", "automation_logs", ["order_id"])
    op.create_index("ix_automation_logs_task_type", "automation_logs", ["task_type"])
    op.create_index(
        "ix_automation_logs_task_status_only", "automation_logs", ["task_status"]
    )
    op.create_index("ix_automation_logs_created_at", "automation_logs", ["created_at"])
    op.create_index(
        "ix_automation_logs_task_status",
        "automation_logs",
        ["task_type", "task_status"],
    )
    op.create_index(
        "ix_automation_logs_app_task",
        "automation_logs",
        ["application_id", "task_type"],
    )


def downgrade() -> None:
    """删除automation_logs表"""

    # 删除索引
    op.drop_index("ix_automation_logs_app_task")
    op.drop_index("ix_automation_logs_task_status")
    op.drop_index("ix_automation_logs_created_at")
    op.drop_index("ix_automation_logs_task_status_only")
    op.drop_index("ix_automation_logs_task_type")
    op.drop_index("ix_automation_logs_order_id")
    op.drop_index("ix_automation_logs_application_id")

    # 删除表
    op.drop_table("automation_logs")
