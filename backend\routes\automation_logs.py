"""
Automation Logs API - 自动化任务状态查询
=====================================

专门用于查询 automation_logs 表的状态，支持前端轮询
✅ 职责清晰：只负责任务状态，不涉及订单管理
"""

from datetime import datetime
from typing import Any
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from sqlalchemy import and_, desc, select, update

from app.data.models.automation_logs import AutomationLogs
from app.data.models.order import Order
from app.data.models.visa_status_history import VisaStatusHistory
from backend.auth_fastapi_users.auth import current_user
from backend.auth_fastapi_users.database import get_async_session

router = APIRouter(prefix="/api/automation-logs", tags=["automation-logs"])


@router.get("/status/{order_no}")
async def get_task_status_by_order(order_no: str, user=Depends(current_user)):
    """
    根据订单编号查询任务状态

    ✅ 专门用于前端轮询，查询 automation_logs 状态
    ✅ 权限控制：只能查询自己的订单任务状态
    """
    try:
        async with get_async_session() as session:
            # 1. 先查找订单，验证权限
            order_result = await session.execute(
                select(Order).where(
                    and_(Order.order_no == order_no, Order.user_id == user.id)
                )
            )
            order = order_result.scalar_one_or_none()

            if not order:
                raise HTTPException(status_code=404, detail="订单不存在")

            # 2. 查询最新的 automation_logs 记录
            log_result = await session.execute(
                select(AutomationLogs)
                .where(AutomationLogs.order_id == order.id)
                .order_by(desc(AutomationLogs.created_at))
                .limit(1)
            )
            log = log_result.scalar_one_or_none()

            if not log:
                # 如果没有 automation_logs 记录，说明任务还没开始
                return {
                    "success": True,
                    "data": {
                        "order_no": order_no,
                        "status": "pending",  # 等待开始
                        "message": "任务等待开始",
                        "updated_at": order.created_at.isoformat(),
                    },
                }

            # 3. 返回任务状态
            return {
                "success": True,
                "data": {
                    "order_no": order_no,
                    "status": log.task_status,
                    "message": log.error_message or "任务状态正常",
                    "task_type": log.task_type,
                    "celery_task_id": log.celery_task_id,
                    "started_at": log.started_at.isoformat()
                    if log.started_at
                    else None,
                    "completed_at": log.completed_at.isoformat()
                    if log.completed_at
                    else None,
                    "updated_at": log.updated_at.isoformat(),
                },
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")


@router.get("/status/batch")
async def get_task_status_batch(
    order_nos: str = Query(..., description="订单编号列表，逗号分隔"),
    user=Depends(current_user),
):
    """
    批量查询任务状态

    ✅ 用于前端轮询多个订单的状态
    ✅ 提高查询效率，减少API调用次数
    """
    try:
        order_no_list = [no.strip() for no in order_nos.split(",") if no.strip()]

        if not order_no_list:
            raise HTTPException(status_code=400, detail="订单编号列表不能为空")

        if len(order_no_list) > 20:
            raise HTTPException(status_code=400, detail="一次最多查询20个订单")

        async with get_async_session() as session:
            # 1. 查询用户的订单
            orders_result = await session.execute(
                select(Order).where(
                    and_(Order.order_no.in_(order_no_list), Order.user_id == user.id)
                )
            )
            orders = orders_result.scalars().all()

            if not orders:
                return {"success": True, "data": []}

            # 2. 查询这些订单的最新 automation_logs
            order_ids = [order.id for order in orders]
            logs_result = await session.execute(
                select(AutomationLogs)
                .where(AutomationLogs.order_id.in_(order_ids))
                .order_by(AutomationLogs.order_id, desc(AutomationLogs.created_at))
            )
            logs = logs_result.scalars().all()

            # 3. 组织数据：每个订单对应最新的日志
            # order_map = {order.id: order for order in orders}  # 未使用，已注释
            latest_logs = {}

            for log in logs:
                if log.order_id not in latest_logs:
                    latest_logs[log.order_id] = log

            # 4. 构建响应
            result = []
            for order in orders:
                log = latest_logs.get(order.id)

                if log:
                    status_data = {
                        "order_no": order.order_no,
                        "status": log.task_status,
                        "message": log.error_message or "任务状态正常",
                        "task_type": log.task_type,
                        "updated_at": log.updated_at.isoformat(),
                    }
                else:
                    status_data = {
                        "order_no": order.order_no,
                        "status": "pending",
                        "message": "任务等待开始",
                        "task_type": None,
                        "updated_at": order.created_at.isoformat(),
                    }

                result.append(status_data)

            return {"success": True, "data": result}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量查询任务状态失败: {str(e)}")


# 🔥 新架构：内部API接口（供Celery调用，无需认证）


class TaskStartedRequest(BaseModel):
    task_id: str
    order_no: str
    user_id: str
    task_type: str = "vietnam_evisa"


class TaskCompletedRequest(BaseModel):
    task_id: str
    status: str  # success/failed/cancelled
    result: dict[str, Any] | None = None
    error_message: str | None = None


@router.post("/internal/task-started")
async def task_started_notification(request: TaskStartedRequest):
    """
    内部API：Celery任务开始通知
    职责：创建automation_logs记录，状态为processing
    """
    try:
        async with get_async_session() as session:
            # 1. 查询订单和申请信息
            order_result = await session.execute(
                select(Order).where(
                    and_(
                        Order.order_no == request.order_no,
                        Order.user_id == request.user_id,
                    )
                )
            )
            order = order_result.scalar_one_or_none()

            if not order:
                raise HTTPException(status_code=404, detail="订单不存在")

            # 2. 查询申请信息
            from app.data.models.application import Application

            app_result = await session.execute(
                select(Application).where(Application.order_id == order.id)
            )
            application = app_result.scalar_one_or_none()

            if not application:
                raise HTTPException(status_code=404, detail="申请信息不存在")

            # 3. 创建automation_logs记录
            log_entry = AutomationLogs(
                id=str(uuid.uuid4()),
                application_id=application.id,
                order_id=order.id,
                task_type=request.task_type,
                task_status="processing",
                celery_task_id=request.task_id,
                started_at=datetime.now(),
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

            session.add(log_entry)
            await session.commit()

            return {"message": "任务开始记录已创建", "task_id": request.task_id}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建任务记录失败: {str(e)}")


@router.post("/internal/task-completed")
async def task_completed_notification(request: TaskCompletedRequest):
    """
    内部API：Celery任务完成通知
    职责：
    1. 更新automation_logs记录，设置最终状态和completed_at
    2. 根据任务结果创建visa_status_history记录
    🔥 新架构：FastAPI统一处理数据库更新，Celery通过HTTP通知状态变更
    """
    try:
        async with get_async_session() as session:
            # 1. 先查询automation_logs记录，获取application_id
            log_result = await session.execute(
                select(AutomationLogs).where(
                    AutomationLogs.celery_task_id == request.task_id
                )
            )
            log_entry = log_result.scalar_one_or_none()

            if not log_entry:
                raise HTTPException(status_code=404, detail="任务记录不存在")

            # 2. 更新automation_logs记录
            update_data = {
                "task_status": request.status,
                "completed_at": datetime.now(),
                "updated_at": datetime.now(),
            }

            if request.error_message:
                update_data["error_message"] = request.error_message

            await session.execute(
                update(AutomationLogs)
                .where(AutomationLogs.celery_task_id == request.task_id)
                .values(**update_data)
            )

            # 3. 根据任务状态创建visa_status_history记录
            # 🎯 业务逻辑：只有完成的任务（无论成功失败取消）才记录到签证状态历史
            if request.status in ["success", "failed", "cancelled"]:
                # 获取用户信息作为operator
                from app.data.models.order import Order
                from app.data.models.user import User

                order_result = await session.execute(
                    select(Order).where(Order.id == log_entry.order_id)
                )
                order = order_result.scalar_one_or_none()

                if not order:
                    raise HTTPException(status_code=404, detail="订单不存在")

                # 获取用户名
                user_result = await session.execute(
                    select(User.username).where(User.id == order.user_id)
                )
                username = user_result.scalar_one_or_none()

                if not username:
                    raise HTTPException(status_code=404, detail="用户不存在")

                # 🎯 精确区分不同状态和对应的备注
                if request.status == "success":
                    visa_status = "submitted"
                    remark = "自动化任务成功完成，申请已提交到越南官方系统"
                elif request.status == "failed":
                    visa_status = "submit_failure"
                    remark = (
                        f"自动化任务执行失败: {request.error_message or '未知错误'}"
                    )
                elif request.status == "cancelled":
                    visa_status = "submit_failure"
                    remark = "自动化任务被用户取消"
                else:
                    # 理论上不会到这里，但为了安全起见
                    visa_status = "submit_failure"
                    remark = f"自动化任务异常状态: {request.status}"

                # 创建visa_status_history记录
                status_history = VisaStatusHistory(
                    user_id=order.user_id,  # 🔧 修复：添加必需的user_id字段
                    application_id=log_entry.application_id,
                    order_id=log_entry.order_id,  # 🔧 修复：添加必需的order_id字段
                    visa_status=visa_status,
                    operator=username,  # 记录用户名，更易读
                    remark=remark,
                )

                session.add(status_history)

            await session.commit()

            return {"message": "任务状态已更新", "task_id": request.task_id}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新任务状态失败: {str(e)}")
