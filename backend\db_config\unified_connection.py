"""
统一数据库连接管理器
按照GG Notepad要求，统一使用SQLAlchemy连接池，替换asyncpg连接
单一数据源，避免连接池竞争和连接泄漏

核心原则
- 全局单例SQLAlchemy引擎
- 动态调整连接池参数
- 环境隔离配置
- 连接健康检
- 资源自动清理
"""

from contextlib import asynccontextmanager
import logging
from typing import Any, Optional

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from backend.config.settings import settings

logger = logging.getLogger(__name__)


class UnifiedDatabaseManager:
    """
    统一数据库连接管理器 - 单例模式
    替换所有asyncpg和多SQLAlchemy引擎
    """

    _instance: Optional["UnifiedDatabaseManager"] = None
    _initialized: bool = False

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化管理器 - 只初始化一次"""
        if self._initialized:
            return
        self.engine: AsyncEngine | None = None
        self.async_session: sessionmaker | None = None
        self.connection_string: str = self._build_connection_string()

        # 连接池配置 - 根据环境动态调整
        self.pool_config = self._get_pool_config()

        self._initialized = True
        logger.info("🚀 统一数据库管理器初始化完成")

    def _build_connection_string(self) -> str:
        """构建异步连接字符串"""
        return (
            f"postgresql+asyncpg://{settings.postgres_user}:{settings.postgres_password}"
            f"@{settings.postgres_host}:{settings.postgres_port}/{settings.postgres_db}"
        )

    def _get_pool_config(self) -> dict[str, Any]:
        """获取连接池配置 根据环境优化"""

        # 检测运行环境
        is_production = (
            getattr(settings, "environment", "development").lower() == "production"
        )
        is_testing = (
            getattr(settings, "environment", "development").lower() == "testing"
        )

        if is_production:
            # 生产环境：更大的连接池，更长的超时
            return {
                "pool_size": 30,
                "max_overflow": 50,
                "pool_timeout": 60,
                "pool_recycle": 3600,
                "pool_pre_ping": True,
                "echo": False,
            }
        elif is_testing:
            # 测试环境：更小的连接池，快速回滚
            return {
                "pool_size": 5,
                "max_overflow": 10,
                "pool_timeout": 30,
                "pool_recycle": 900,  # 15分钟回收
                "pool_pre_ping": True,
                "echo": False,
            }
        else:
            # 开发环境：中等连接池，启用调试
            return {
                "pool_size": 10,
                "max_overflow": 20,
                "pool_timeout": 30,
                "pool_recycle": 1800,  # 30分钟回收
                "pool_pre_ping": True,
                "echo": False,  # 关闭SQLAlchemy内置日志，使用我们的统一日志系统
            }

    async def initialize(self) -> bool:
        """
        初始化数据库引擎和会话
        确保资源正确初始化
        """
        try:
            logger.info(f"🔗 初始化统一数据库连接 {self.connection_string}")

            # 创建异步引擎
            self.engine = create_async_engine(
                self.connection_string, **self.pool_config
            )

            # 创建异步会话工厂
            self.async_session = sessionmaker(
                bind=self.engine, class_=AsyncSession, expire_on_commit=False
            )

            # 测试连接
            await self._test_connection()

            logger.info(f"🔗 统一数据库连接初始化成功，连接池配置: {self.pool_config}")
            return True
        except Exception as e:
            logger.error(f"🔗 统一数据库连接初始化失败: {e}")
            return False

    async def _test_connection(self):
        """测试数据库连接"""
        if not self.engine:
            raise Exception("数据库引擎未初始化")

        async with self.engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            assert result.scalar() == 1

        logger.info("🔗 数据库连接测试通过")

    @asynccontextmanager
    async def get_session(self):
        """获取数据库会话 上下文管理器
        自动清理资源，符合GG Notepad要求"""
        if not self.async_session:
            raise Exception("数据库会话工厂未初始化")

        async with self.async_session() as session:
            try:
                yield session
            except Exception as e:
                logger.error(f"🔗 数据库会话异常: {e}")
                await session.rollback()
                raise
            finally:
                await session.close()

    @asynccontextmanager
    async def get_connection(self):
        """获取原始数据库连接 用于兼容asyncpg风格的代码
        提供兼容性接口，方便迁移"""
        if not self.engine:
            raise Exception("数据库引擎未初始化")

        async with self.engine.begin() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"🔗 数据库连接异常: {e}")
                raise

    async def execute_query(self, query: str, *args) -> Any:
        """执行单个查询 简化接口
        提供简单的查询接口"""
        async with self.get_connection() as conn:
            result = await conn.execute(text(query), list(args))
            return result

    async def execute(self, query: str, params=None) -> Any:
        """执行查询 - 兼容参数格式"""
        async with self.get_connection() as conn:
            if params is None:
                result = await conn.execute(text(query))
            elif isinstance(params, list | tuple):
                result = await conn.execute(text(query), params)
            else:
                result = await conn.execute(text(query), [params])
            return result

    async def fetch_one(self, query: str, *args) -> dict | None:
        """获取单行记录"""
        async with self.get_connection() as conn:
            result = await conn.execute(text(query), list(args))
            row = result.fetchone()
            return dict(row._mapping) if row else None

    async def fetch_all(self, query: str, *args) -> list:
        """获取所有记录"""
        async with self.get_connection() as conn:
            result = await conn.execute(text(query), list(args))
            rows = result.fetchall()
            return [dict(row._mapping) for row in rows]

    async def fetch_val(self, query: str, *args) -> Any:
        """获取单个值"""
        async with self.get_connection() as conn:
            result = await conn.execute(text(query), list(args))
            row = result.fetchone()
            return row[0] if row else None

    def get_stats(self) -> dict[str, Any]:
        """获取连接池统计信息 用于监控和调优"""
        if not self.engine or not self.engine.pool:
            return {"status": "not_initialized"}

        pool = self.engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
            "status": "active",
        }

    async def get_orders_based_count_records(self) -> dict[str, Any]:
        """获取基于订单的统计记录"""
        try:
            query = """
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN order_status = 'created' THEN 1 END) as created_orders,
                COUNT(CASE WHEN order_status = 'cancelled' THEN 1 END) as cancelled_orders,
                0 as success_orders,
                0 as failed_orders,
                0 as processing_orders
            FROM "order"
            """

            result = await self.fetch_one(query)
            return result or {}

        except Exception as e:
            logger.error(f"❌ 获取订单统计失败: {e}")
            return {}

    async def get_orders_based_time_series_stats(
        self, period: str = "7d"
    ) -> dict[str, Any]:
        """获取基于订单的时间序列统计"""
        try:
            # 根据周期确定时间范围
            if period == "1d":
                interval = "1 day"
                date_format = "HH24:00"
            elif period == "7d":
                interval = "7 days"
                date_format = "YYYY-MM-DD"
            elif period == "30d":
                interval = "30 days"
                date_format = "YYYY-MM-DD"
            elif period == "90d":
                interval = "90 days"
                date_format = "YYYY-MM-DD"
            elif period == "1y":
                interval = "365 days"
                date_format = "YYYY-MM"
            else:
                interval = "7 days"
                date_format = "YYYY-MM-DD"

            query = f"""
            SELECT
                TO_CHAR(created_at, '{date_format}') as date_key,
                COUNT(*) as total,
                COUNT(CASE WHEN order_status = 'created' THEN 1 END) as created,
                COUNT(CASE WHEN order_status = 'cancelled' THEN 1 END) as cancelled,
                0 as success,
                0 as failed
            FROM "order"
            WHERE created_at >= NOW() - INTERVAL '{interval}'
            GROUP BY TO_CHAR(created_at, '{date_format}')
            ORDER BY date_key
            """

            result = await self.fetch_all(query)
            return {"data": result or [], "period": period}

        except Exception as e:
            logger.error(f"❌ 获取时间序列统计失败: {e}")
            return {"data": [], "period": period}

    async def get_orders_based_status_distribution(self) -> dict[str, Any]:
        """获取订单状态分布统计"""
        try:
            query = """
            SELECT
                order_status as status,
                COUNT(*) as count
            FROM "order"
            GROUP BY order_status
            ORDER BY count DESC
            """

            result = await self.fetch_all(query)
            return {"data": result or []}

        except Exception as e:
            logger.error(f"❌ 获取状态分布统计失败: {e}")
            return {"data": []}

    async def get_orders_based_entry_gate_stats(self) -> dict[str, Any]:
        """获取入境口岸统计"""
        try:
            query = """
            SELECT
                COALESCE(
                    ap.form_snapshot->>'intended_entry_gate',
                    '未指定'
                ) as entry_gate,
                COUNT(*) as count
            FROM "order" o
            LEFT JOIN application ap ON o.id = ap.order_id
            WHERE ap.form_snapshot IS NOT NULL
            GROUP BY ap.form_snapshot->>'intended_entry_gate'
            ORDER BY count DESC
            LIMIT 10
            """

            result = await self.fetch_all(query)
            return {"data": result or []}

        except Exception as e:
            logger.error(f"❌ 获取入境口岸统计失败: {e}")
            return {"data": []}

    async def get_orders_based_visa_type_stats(self) -> dict[str, Any]:
        """获取签证类型统计"""
        try:
            query = """
            SELECT
                COALESCE(
                    ap.form_snapshot->>'visa_entry_type',
                    '未指定'
                ) as visa_type,
                COUNT(*) as count
            FROM "order" o
            LEFT JOIN application ap ON o.id = ap.order_id
            WHERE ap.form_snapshot IS NOT NULL
            GROUP BY ap.form_snapshot->>'visa_entry_type'
            ORDER BY count DESC
            """

            result = await self.fetch_all(query)
            return {"data": result or []}

        except Exception as e:
            logger.error(f"❌ 获取签证类型统计失败: {e}")
            return {"data": []}

    async def get_orders_based_processing_time_stats(self) -> dict[str, Any]:
        """获取处理时间统计"""
        try:
            query = """
            SELECT
                AVG(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as avg_hours,
                MIN(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as min_hours,
                MAX(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as max_hours,
                COUNT(*) as total_processed
            FROM "order"
            WHERE order_status IN ('created', 'cancelled')
            AND updated_at > created_at
            """

            result = await self.fetch_one(query)
            return result or {}

        except Exception as e:
            logger.error(f"❌ 获取处理时间统计失败: {e}")
            return {}

    # 业务查询方法已迁移到相应的Repository中
    # query_orders_with_filters -> OrderRepository.query_orders_with_filters

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self._test_connection()
            return True
        except Exception as e:
            logger.error(f"🔗 数据库健康检查失败: {e}")
            return False

    async def cleanup(self):
        """
        清理资源，应用关闭时调用
        确保连接池正确关闭，避免事件循环冲突
        """
        if self.engine:
            try:
                # 优雅关闭连接池
                await self.engine.dispose()
                logger.info("✅ 统一数据库连接池已清理")
            except Exception as e:
                logger.warning(f"⚠️ 清理数据库连接池时出现警告: {e}")
            finally:
                self.engine = None
                self.async_session = None


# 全局单例示例
_unified_db_manager: UnifiedDatabaseManager | None = None


async def get_unified_db() -> UnifiedDatabaseManager:
    """
    获取统一数据库管理实例
    全局访问
    """
    global _unified_db_manager

    if _unified_db_manager is None:
        _unified_db_manager = UnifiedDatabaseManager()

        # 确保初始化
        if not await _unified_db_manager.initialize():
            raise Exception("统一数据库管理器初始化失败")

    return _unified_db_manager


# 便捷函数 向后兼容
async def get_db_session():
    """获取数据库会话"""
    db = await get_unified_db()
    return db.get_session()


async def get_db_connection():
    """获取原始数据库连接"""
    db = await get_unified_db()
    return db.get_connection()
