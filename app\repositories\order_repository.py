"""
订单Repository - 订单数据访问层
===============================

负责订单相关的所有数据库操作，隔离业务逻辑与数据访问技术。
支持Session注入，符合依赖注入原则。
"""

from datetime import datetime, time, timedelta
import logging
from typing import Any
from uuid import UUID

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.data.models.applicant import Applicant
from app.data.models.application import Application
from app.data.models.order import Order
from backend.models.order import OrderStatus

from .base import SQLAlchemyRepository

logger = logging.getLogger(__name__)


def get_day_boundary(dt, boundary: str = "start") -> datetime:
    """
    Helper to get the start or end of the day for a given date or datetime.
    Args:
        dt: date or datetime object
        boundary: "start" for 00:00:00, "end" for 23:59:59.999999
    Returns:
        datetime object at the boundary of the day
    """
    if hasattr(dt, "date"):
        dt = dt.date()
    if boundary == "start":
        return datetime.combine(dt, time.min)
    elif boundary == "end":
        return datetime.combine(dt, time.max)
    else:
        raise ValueError("boundary must be 'start' or 'end'")


class OrderRepository(SQLAlchemyRepository[Order, UUID]):
    """订单Repository - 专门处理订单数据访问"""

    def __init__(self, session: AsyncSession):
        """
        接受Session注入
        """
        super().__init__(session, Order)

    async def get_by_order_no(
        self, order_no: str, user_id: UUID | None = None
    ) -> Order | None:
        """
        根据订单号获取订单

        Args:
            order_no: 订单编号
            user_id: 用户ID，如果提供则进行权限检查
        """
        query = select(Order).where(Order.order_no == order_no)

        # 🔐 安全修复：如果提供了user_id，则进行权限检查
        if user_id is not None:
            query = query.where(Order.user_id == user_id)

        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def get_by_user_id(
        self,
        user_id: UUID,
        status: OrderStatus | None = None,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Order]:
        """获取用户的订单列表"""
        query = select(Order).where(Order.user_id == user_id)

        if status:
            query = query.where(Order.order_status == status.value)

        query = query.order_by(desc(Order.created_at)).limit(limit).offset(offset)

        result = await self.session.execute(query)
        return result.scalars().all()

    async def count_by_user_id(
        self, user_id: UUID, status: OrderStatus | None = None
    ) -> int:
        """统计用户订单数量"""
        query = select(func.count(Order.id)).where(Order.user_id == user_id)

        if status:
            query = query.where(Order.order_status == status.value)

        result = await self.session.execute(query)
        return result.scalar()

    async def get_orders_by_status(
        self,
        status: OrderStatus,
        limit: int = 100,
        offset: int = 0,
        user_id: UUID = None,
    ) -> list[Order]:
        """
        根据状态获取订单列表

        Args:
            status: 订单状态
            limit: 限制数量
            offset: 偏移量
            user_id: 用户ID，必须提供以确保权限控制

        🔐 安全要求：必须提供user_id
        """
        if user_id is None:
            raise ValueError("安全错误：必须提供user_id进行权限控制")

        query = select(Order).where(
            and_(Order.order_status == status.value, Order.user_id == user_id)
        )

        query = query.order_by(desc(Order.created_at)).limit(limit).offset(offset)
        result = await self.session.execute(query)
        return result.scalars().all()

    async def get_recent_orders(
        self, hours: int = 24, limit: int = 50, user_id: UUID = None
    ) -> list[Order]:
        """
        获取最近的订单

        Args:
            hours: 时间范围（小时）
            limit: 限制数量
            user_id: 用户ID，必须提供以确保权限控制

        🔐 安全要求：必须提供user_id
        """
        if user_id is None:
            raise ValueError("安全错误：必须提供user_id进行权限控制")

        since = datetime.utcnow() - timedelta(hours=hours)
        query = select(Order).where(
            and_(Order.created_at >= since, Order.user_id == user_id)
        )

        query = query.order_by(desc(Order.created_at)).limit(limit)
        result = await self.session.execute(query)
        return result.scalars().all()

    async def update_status(
        self, order_no: str, new_status: OrderStatus, user_id: UUID | None = None
    ) -> bool:
        """
        更新订单状态

        Args:
            order_no: 订单编号
            new_status: 新状态
            user_id: 用户ID，如果提供则进行权限检查
        """
        query = select(Order).where(Order.order_no == order_no)

        # 🔐 安全修复：如果提供了user_id，则进行权限检查
        if user_id is not None:
            query = query.where(Order.user_id == user_id)

        result = await self.session.execute(query)
        order = result.scalar_one_or_none()
        if not order:
            return False

        order.order_status = new_status.value
        order.updated_at = datetime.utcnow()

        await self.session.commit()
        return True

    async def get_order_stats(self, user_id: UUID | None = None) -> dict[str, Any]:
        """获取订单统计信息"""
        # 🔧 修复：直接使用WHERE条件，避免笛卡尔积
        base_conditions = []
        if user_id:
            base_conditions.append(Order.user_id == user_id)

        # 总订单数
        total_query = select(func.count(Order.id))
        if base_conditions:
            total_query = total_query.where(and_(*base_conditions))

        total_result = await self.session.execute(total_query)
        total_orders = total_result.scalar()

        # 按状态统计
        status_stats = {}
        for status in OrderStatus:
            status_conditions = base_conditions.copy()
            status_conditions.append(Order.order_status == status.value)

            status_query = select(func.count(Order.id)).where(and_(*status_conditions))
            result = await self.session.execute(status_query)
            status_stats[status.value] = result.scalar()

        # 今日订单数
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        today_conditions = base_conditions.copy()
        today_conditions.append(Order.created_at >= today)

        today_query = select(func.count(Order.id)).where(and_(*today_conditions))
        today_result = await self.session.execute(today_query)
        today_orders = today_result.scalar()

        return {
            "total_orders": total_orders,
            "today_orders": today_orders,
            "status_breakdown": status_stats,
            "generated_at": datetime.utcnow().isoformat(),
        }

    async def check_duplicate_order(
        self, user_id: UUID, idempotent_key: str, within_seconds: int = 60
    ) -> Order | None:
        """检查重复订单（基于幂等性键）"""
        result = await self.session.execute(
            select(Order).where(
                and_(
                    Order.user_id == user_id,
                    Order.idempotent_key == idempotent_key,
                    Order.created_at
                    >= datetime.utcnow() - timedelta(seconds=within_seconds),
                )
            )
        )
        return result.scalar_one_or_none()

    async def query_user_orders(self, user_id: int, params) -> dict[str, Any]:
        """
        查询用户订单列表 - OrderService调用的核心方法

        Args:
            user_id (int): 用户ID
            params: 查询参数（OrderQueryParams类型）

        Returns:
            Dict[str, Any]: 标准化响应格式
        """
        try:
            # 🔐 构建基础查询 - 确保用户权限隔离
            query = select(Order).where(Order.user_id == user_id)
            count_query = select(func.count(Order.id)).where(Order.user_id == user_id)

            # 应用筛选条件
            if hasattr(params, "status") and params.status:
                query = query.where(Order.order_status == params.status.value)
                count_query = count_query.where(
                    Order.order_status == params.status.value
                )

            if hasattr(params, "order_no") and params.order_no:
                # 支持模糊查询
                condition = Order.order_no.ilike(f"%{params.order_no}%")
                query = query.where(condition)
                count_query = count_query.where(condition)

            # 日期范围筛选 - 使用naive datetime（数据库存储格式）
            if hasattr(params, "date_from") and params.date_from:
                date_from_start = get_day_boundary(params.date_from, "start")
                condition = Order.created_at >= date_from_start
                query = query.where(condition)
                count_query = count_query.where(condition)

            if hasattr(params, "date_to") and params.date_to:
                date_to_end = get_day_boundary(params.date_to, "end")
                condition = Order.created_at <= date_to_end
                query = query.where(condition)
                count_query = count_query.where(condition)

            # 获取总数
            count_result = await self.session.execute(count_query)
            total_count = count_result.scalar()

            # 应用分页和排序
            query = query.order_by(desc(Order.created_at))
            limit = getattr(params, "limit", 20)
            page = getattr(params, "page", 1)
            offset = (page - 1) * limit

            query = query.limit(limit).offset(offset)

            # 执行查询
            result = await self.session.execute(query)
            orders = result.scalars().all()

            # 转换为字典格式
            order_list = []
            for order in orders:
                order_dict = {
                    "id": str(order.id),
                    "order_no": order.order_no,
                    "user_id": order.user_id,
                    "order_status": order.order_status,
                    "created_at": order.created_at.isoformat(),
                    "updated_at": order.updated_at.isoformat(),
                }
                order_list.append(order_dict)

            # 计算分页信息
            total_pages = (total_count + limit - 1) // limit

            pagination_info = {
                "current_page": page,
                "total_pages": total_pages,
                "total_items": total_count,
                "has_prev": page > 1,
                "has_next": page < total_pages,
                "page_size": limit,
            }

            return {
                "success": True,
                "data": {"orders": order_list, "pagination": pagination_info},
                "message": "查询成功",
            }

        except Exception as e:
            logger.error(f"查询用户订单失败: {str(e)}", exc_info=True)
            return {"success": False, "message": f"查询订单失败: {str(e)}"}

    async def update_order_status(
        self,
        user_id: int,
        order_no: str,
        new_status,
        reason: str = None,
        operator_id: int = None,
        operator_type=None,
    ) -> dict[str, Any]:
        """
        更新订单状态 - OrderService调用的核心方法

        Args:
            user_id (int): 用户ID，用于权限校验
            order_no (str): 订单编号
            new_status: 新状态（OrderStatus类型）
            reason (str): 状态变更原因
            operator_id (int): 操作者ID
            operator_type: 操作者类型（OperatorType）

        Returns:
            Dict[str, Any]: 标准化响应格式
        """
        try:
            # 1. 获取订单并验证权限
            result = await self.session.execute(
                select(Order).where(Order.order_no == order_no)
            )
            order = result.scalar_one_or_none()

            if not order:
                return {"success": False, "message": "订单不存在"}

            # 权限校验：确保用户只能更新自己的订单
            if order.user_id != user_id:
                return {"success": False, "message": "无权限访问该订单"}

            # 2. 记录原状态
            old_status = order.order_status

            # 3. 更新订单状态
            status_value = (
                new_status.value if hasattr(new_status, "value") else str(new_status)
            )
            order.order_status = status_value
            order.updated_at = datetime.utcnow()

            # 4. 提交事务
            await self.session.commit()
            await self.session.refresh(order)

            # 5. 返回成功响应
            return {
                "success": True,
                "data": {
                    "order_no": order.order_no,
                    "old_status": old_status,
                    "new_status": status_value,
                    "updated_at": order.updated_at.isoformat(),
                },
                "message": f"订单状态已从 {old_status} 更新为 {status_value}",
            }

        except Exception as e:
            # 回滚事务
            await self.session.rollback()
            return {"success": False, "message": f"更新订单状态失败: {str(e)}"}

    # 注意：根据正确的数据库架构，申请数据存储在Application表中
    # 此方法已移除，应该通过ApplicationRepository来处理申请数据

    async def query_orders_with_filters(
        self,
        filters: dict[str, Any] | None = None,
        limit: int = 1000,
        user_id: UUID | None = None,
    ) -> list[dict[str, Any]]:
        """
        根据过滤条件查询订单（包含关联信息）

        Args:
            filters: 过滤条件
            limit: 限制数量
            user_id: 用户ID，如果提供则只返回该用户的订单

        ⚠️ 安全要求：必须提供user_id或在filters中包含user_id
        """
        filters = filters or {}

        # 🔐 安全检查：必须有用户权限限制
        effective_user_id = user_id or filters.get("user_id")
        if effective_user_id is None:
            raise ValueError("安全错误：必须提供user_id进行权限控制")

        # 构建基础查询
        query = (
            select(
                Order.id,
                Order.order_no,
                Order.user_id,
                Order.order_status,
                Order.created_at,
                Order.updated_at,
                Applicant.passport_number,
                Applicant.surname,
                Applicant.given_name,
                Applicant.chinese_name,
                Applicant.date_of_birth,
                Applicant.email,
                Applicant.telephone_number,
                Application.vietnam_application_number,
                Application.form_snapshot.label("application_data"),
            )
            .select_from(Order)
            .outerjoin(Application, Order.id == Application.order_id)
            .outerjoin(Applicant, Application.applicant_id == Applicant.id)
        )

        # 添加过滤条件
        conditions = []

        # 🔐 强制用户权限检查
        conditions.append(Order.user_id == effective_user_id)

        if filters.get("status"):
            conditions.append(Order.order_status == filters["status"])

        if filters.get("order_status"):
            conditions.append(Order.order_status == filters["order_status"])

        if filters.get("date_from"):
            date_from_start = get_day_boundary(filters["date_from"], "start")
            conditions.append(Order.created_at >= date_from_start)
            logger.info(
                f"📅 Repository日期筛选 - 开始时间: {filters['date_from']} -> {date_from_start}"
            )

        if filters.get("date_to"):
            date_to_end = get_day_boundary(filters["date_to"], "end")
            conditions.append(Order.created_at <= date_to_end)
            logger.info(
                f"📅 Repository日期筛选 - 结束时间: {filters['date_to']} -> {date_to_end}"
            )

        if conditions:
            query = query.where(and_(*conditions))

        # 添加排序和限制
        query = query.order_by(desc(Order.created_at)).limit(limit)

        result = await self.session.execute(query)
        rows = result.fetchall()

        logger.info(f"📊 查询结果: {len(rows)}条记录")
        return rows
