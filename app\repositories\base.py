"""
Repository基类 - 数据访问层抽取
================================

提供通用的CRUD操作接口，所有具体Repository继承此基类
支持Session注入，符合依赖注入原则
"""

from abc import ABC, abstractmethod
from typing import Generic, TypeVar

from sqlalchemy.ext.asyncio import AsyncSession

# 泛型类型变量
T = TypeVar("T")  # 领域对象类型
ID = TypeVar("ID")  # 主键类型


class BaseRepository(Generic[T, ID], ABC):
    """Repository基类 - 定义通用数据访问接口"""

    def __init__(self, session: AsyncSession):
        """
        正确的依赖注入方式 Session由外部（FastAPI Depends）传入
        """
        self.session = session

    @abstractmethod
    async def get_by_id(self, id: ID) -> T | None:
        """根据ID获取实体"""
        pass

    @abstractmethod
    async def create(self, entity: T) -> T:
        """创建新实体"""
        pass

    @abstractmethod
    async def update(self, entity: T) -> T:
        """更新实体"""
        pass

    @abstractmethod
    async def delete(self, id: ID) -> bool:
        """删除实体"""
        pass

    @abstractmethod
    async def list(self, limit: int = 100, offset: int = 0) -> list[T]:
        """分页获取实体列表"""
        pass

    @abstractmethod
    async def count(self) -> int:
        """获取实体总数"""
        pass


class SQLAlchemyRepository(BaseRepository[T, ID]):
    """基于SQLAlchemy的Repository实现"""

    def __init__(self, session: AsyncSession, model_class):
        """
        接受Session和模型类
        """
        super().__init__(session)
        self.model_class = model_class

    async def get_by_id(self, id: ID) -> T | None:
        """根据ID获取实体"""
        from sqlalchemy import select

        result = await self.session.execute(
            select(self.model_class).where(self.model_class.id == id)
        )
        return result.scalar_one_or_none()

    async def create(self, entity: T) -> T:
        """创建新实体"""
        self.session.add(entity)
        await self.session.commit()
        await self.session.refresh(entity)
        return entity

    async def update(self, entity: T) -> T:
        """更新实体"""
        self.session.add(entity)
        await self.session.commit()
        await self.session.refresh(entity)
        return entity

    async def delete(self, id: ID) -> bool:
        """删除实体"""
        from sqlalchemy import select

        result = await self.session.execute(
            select(self.model_class).where(self.model_class.id == id)
        )
        entity = result.scalar_one_or_none()
        if entity:
            await self.session.delete(entity)
            await self.session.commit()
            return True
        return False

    async def list(self, limit: int = 100, offset: int = 0) -> list[T]:
        """分页获取实体列表"""
        from sqlalchemy import select

        result = await self.session.execute(
            select(self.model_class).limit(limit).offset(offset)
        )
        return result.scalars().all()

    async def count(self) -> int:
        """获取实体总数"""
        from sqlalchemy import func, select

        result = await self.session.execute(select(func.count(self.model_class.id)))
        return result.scalar()
