import { api } from './request'
import type { DuplicateCheckResponse, OcrResponse, VisaApplicationResponse } from './types'

// 签证申请API
export const visaApi = {
  // 检查重复提交，严格对应旧版 /api/visa/check-duplicate/:passport_number
  checkDuplicate: (passportNumber: string): Promise<DuplicateCheckResponse> => {
    return api
      .get(`/api/visa/check-duplicate/${passportNumber}`)
      .then((response) => response.data as DuplicateCheckResponse)
  },

  // 提交签证申请，严格对应旧版 /api/visa/apply
  submitApplication: (formData: FormData): Promise<VisaApplicationResponse> => {
    return api
      .upload('/api/visa/apply', formData)
      .then((response) => response.data as VisaApplicationResponse)
  },

  // OCR护照识别，统一使用 /ocr-passport/ 端点
  ocrPassport: (file: File, signal?: AbortSignal): Promise<OcrResponse> => {
    const formData = new FormData()
    formData.append('passport_scan', file) // 匹配后端期望的字段名

    // 使用统一的 /ocr-passport/ 端点
    return api
      .upload('/ocr-passport/', formData, {
        signal, // 支持取消请求
        timeout: 60000, // OCR处理可能需要更长时间
      })
      .then((response) => {
        console.log('🔍 OCR API响应:', JSON.stringify(response, null, 2))

        // 修复：api.upload已经返回了response.data，所以response就是OCR数据
        const ocrData = response as unknown as Record<string, unknown> // 直接使用response，不要再访问.data

        console.log('🔍 解析后的OCR数据:', JSON.stringify(ocrData, null, 2))

        // 检查是否有有效的OCR数据
        if (ocrData && typeof ocrData === 'object') {
          const hasValidData = Object.values(ocrData).some(
            (value) => value && typeof value === 'string' && value.trim() !== '',
          )

          if (hasValidData) {
            return {
              success: true,
              data: ocrData,
              message: 'OCR识别成功',
            } as OcrResponse
          }
        }

        return {
          success: false,
          data: undefined,
          message: '未能从护照图片中识别到有效信息',
        } as OcrResponse
      })
      .catch((error) => {
        return {
          success: false,
          data: undefined,
          message: error.message ?? 'OCR识别失败',
        } as OcrResponse
      })
  },

  // 下载申请结果文件
  downloadResult: (applicationNumber: string): Promise<Blob> => {
    return api
      .get(`/api/visa/download/${applicationNumber}`, {
        responseType: 'blob',
      })
      .then((response) => response.data as Blob)
  },
}
