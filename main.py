import asyncio
import contextlib
import os
import sys

sys.path.append(os.path.abspath("."))

from dotenv import load_dotenv
from PyQt6.QtWidgets import QApplication

from app.ui.main_window import MainWindow
from app.utils.logger_config import get_logger, setup_logger
from backend.db_config.unified_connection import get_unified_db

# 在程序开始时加载.env文件
load_dotenv()


# 设置日志级别
setup_logger(console_level="DEBUG")

# ✅ 设置控制台输出编码为 UTF-8（支持中文 + emoji）

with contextlib.suppress(Exception):
    sys.stdout.reconfigure(encoding="utf-8")


# === 应用现代主题样式 ===
def apply_qss(path: str):
    from PyQt6.QtWidgets import QApplication

    with open(path, encoding="utf-8") as f:
        qss = f.read()
        QApplication.instance().setStyleSheet(qss)


# 定义定义用于邮件监听的 SimpleApplicant 类
class SimpleApplicant:
    def __init__(self, email, config):
        self.email = email
        self.config = config
        self.passport_number = (
            f"email_{email.split('@')[0]}"  # 添加唯一的 passport_number
        )


if __name__ == "__main__":
    logger = get_logger()
    logger.info("应用程序启动...")

    # 直接从环境变量加载设置
    from app.utils.env_loader import load_email_accounts_from_env, load_env_var

    # 创建设置字典
    settings = {
        "vietnam_evisa_url": load_env_var("VIETNAM_EVISA_URL", "https://evisa.gov.vn/"),
        "browser": load_env_var("BROWSER", "chromium"),
        "headless": load_env_var("HEADLESS", "true").lower() == "true",
        "slow_mo": int(load_env_var("SLOW_MO", "0")),
        "anti_captcha_api_key": load_env_var("ANTI_CAPTCHA_API_KEY", ""),
    }

    # 获取邮箱配置 - 直接从环境变量加载
    email_configs_data = load_email_accounts_from_env()
    email_configs = list(email_configs_data.get("email_accounts", {}).values())

    # 2.显示数据库统计信息（使用新的数据库工厂模式）
    async def show_database_info():
        try:
            # 检查数据库健康状态
            db = await get_unified_db()
            health_ok = await db.health_check()
            logger.info("📊 数据库类型: postgresql")
            logger.info(f"📊 连接状态: {'healthy' if health_ok else 'unhealthy'}")

            # 获取数据库统计
            db = await get_unified_db()
            stats = await db.count_records()
            logger.info(
                f"📊 数据库统计: {stats['applicants']} 位申请人, {stats['tasks']} 条签证任务"
            )
            logger.info(
                f"📊 任务状态: {stats['success']} 条成功提交, {stats['downloaded']} 条已下载签证"
            )

            # 显示最近记录
            recent_records = await db.verify_recent_records(5)
            if recent_records:
                logger.info(f"📊 最近 {len(recent_records)} 条记录已加载")
            else:
                logger.info("📊 暂无历史记录")

        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            logger.error("🚨 应用无法启动，请检查PostgreSQL数据库配置")
            sys.exit(1)

    # 运行数据库信息显示
    asyncio.run(show_database_info())

    # 3. 创建 PyQt 应用实例
    app = QApplication(sys.argv)

    # 4. 加载现代化 QSS 样式
    apply_qss("app/ui/styles/modern_card.qss")

    # 5. 创建并显示主窗口
    logger.info("创建主窗口...")
    main_window = MainWindow()
    main_window.show()
    logger.info("主窗口已显示。")

    # 🔥 邮件轮询已移至独立服务
    # 在容器化部署中，邮件轮询由独立的 email-polling 容器处理
    # 在本地开发中，如需邮件轮询功能，请单独启动邮件轮询服务
    logger.info("📧 邮件轮询功能已移至独立服务，主应用不再启动邮件监听")

    # 7. 运行应用事件循环，直到窗口关闭
    logger.info("启动 PyQt 事件循环...")
    try:
        sys.exit(app.exec())
    except SystemExit:
        logger.info("应用程序正常退出。")
    except Exception as e:
        logger.critical(f"应用程序因未处理的异常而终止: {e}", exc_info=True)
        sys.exit(1)  # 返回非零表示错误退出
