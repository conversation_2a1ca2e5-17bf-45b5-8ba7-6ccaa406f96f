import { globalIgnores } from 'eslint/config'
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript'
import pluginVue from 'eslint-plugin-vue'
import pluginVitest from '@vitest/eslint-plugin'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'

// To allow more languages other than `ts` in `.vue` files, uncomment the following lines:
// import { configureVueProject } from '@vue/eslint-config-typescript'
// configureVueProject({ scriptLangs: ['ts', 'tsx'] })
// More info at https://github.com/vuejs/eslint-config-typescript/#advanced-setup

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
  },

  globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

  pluginVue.configs['flat/essential'],
  vueTsConfigs.recommended,
  
  {
    ...pluginVitest.configs.recommended,
    files: ['src/**/__tests__/*'],
  },
  
  // 自定义规则配置
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'warn', // 将any错误降级为警告
      '@typescript-eslint/no-unused-vars': 'warn',  // 将未使用变量降级为警告
      'vue/multi-word-component-names': 'off',      // 关闭组件名必须多单词的限制
      '@typescript-eslint/prefer-promise-reject-errors': 'off', // 允许reject非Error对象
      '@typescript-eslint/prefer-nullish-coalescing': 'warn'    // 建议使用??而不是||
    }
  },
  
  skipFormatting,
)
