# 文件路径建议：app/core/batch_runner.py
import multiprocessing
import random
import time

from app.data.model import VietnamEVisaApplicant
from app.utils.logger_config import get_logger

logger = get_logger()


# 子进程任务入口
def run_applicant_task(applicant: VietnamEVisaApplicant):
    from app.core.visa_automation_engine import VisaAutomationEngine

    # 使用标准方式获取日志记录器
    logger = get_logger()
    try:
        logger.info(
            f"开始提交申请人: {applicant.chinese_name or applicant.given_name} ({applicant.passport_number})"
        )
        engine = VisaAutomationEngine()  # ✅ 默认构造函数自动加载配置
        success = engine.run_vietnam_evisa_step1(applicant)  # ✅ 正确方法名

        if success:
            logger.info(
                f"✅ 申请人 {applicant.chinese_name or applicant.surname or applicant.given_name} 提交成功"
            )
        else:
            logger.error(
                f"❌ 申请人 {applicant.chinese_name or applicant.surname or applicant.given_name} 提交失败"
            )
    except Exception as e:
        logger.critical(
            f"❌ 申请人 {applicant.chinese_name or applicant.surname or applicant.given_name} 执行出错: {e}",
            exc_info=True,
        )


# 内部函数：在随机延迟后启动任务
def _delayed_start(applicant: VietnamEVisaApplicant, delay_sec: int):
    # 使用标准方式获取日志记录器
    logger = get_logger()
    time.sleep(delay_sec)
    logger.info(
        f"⏳ 延迟 {delay_sec} 秒后启动任务：{applicant.passport_number} ({applicant.chinese_name or applicant.given_name})"
    )
    logger.info(f"⏳ 正在启动任务：{applicant.passport_number}")
    run_applicant_task(applicant)


# 批量任务调度器
def run_batch(
    applicant_list: list[VietnamEVisaApplicant],
    max_concurrent: int = 10,
    launch_window: int = 30,
):
    processes = []

    logger.info(
        f"开始批量处理 {len(applicant_list)} 位申请人，最大并发数: {max_concurrent}"
    )

    for idx, applicant in enumerate(applicant_list):
        # ✅ 第一个任务立即执行，其余随机延迟20-60秒
        delay_sec = 0 if idx == 0 else random.randint(20, 60)
        logger.info(
            f"⏳ 计划延迟 {delay_sec} 秒后启动任务 [{idx + 1}/{len(applicant_list)}]：{applicant.passport_number} ({applicant.chinese_name or applicant.given_name})"
        )

        # ✅ 控制最大并发数
        while len(processes) >= max_concurrent:
            processes = [p for p in processes if p.is_alive()]
            logger.info(f"当前活跃进程: {len(processes)}/{max_concurrent}")
            time.sleep(0.5)

        p = multiprocessing.Process(target=_delayed_start, args=(applicant, delay_sec))
        p.start()
        processes.append(p)
        logger.info(
            f"✅ 已启动进程 [{idx + 1}/{len(applicant_list)}] 处理申请人: {applicant.chinese_name}"
        )

    # 等待所有任务完成
    for p in processes:
        p.join()

    logger.info(
        f"✅ 批量处理完成，共处理 {len(applicant_list)} 位申请人，结果请查看日志文件"
    )
