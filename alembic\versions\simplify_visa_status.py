"""simplify visa_status_history design

Revision ID: simplify_visa_status_history_design
Revises: simplify_status_system
Create Date: 2025-06-22 14:30:00.000000

简化visa_status_history表设计：
1. 删除from_status字段（过度设计）
2. 将to_status重命名为visa_status（更清晰）
3. 重命名约束为更合理的名称
4. 添加自动同步机制的基础结构
"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "simplify_visa_status"
down_revision = "fix_applicant_email_constraint"
branch_labels = None
depends_on = None


def upgrade():
    """简化visa_status_history表设计"""
    print("=" * 60)
    print("🔄 开始简化visa_status_history表设计...")
    print("=" * 60)

    # ==================== 1. 备份现有数据 ====================
    print("📋 备份现有数据...")

    try:
        # 检查是否有现有数据
        result = (
            op.get_bind().execute("SELECT COUNT(*) FROM visa_status_history").scalar()
        )

        if result > 0:
            print(f"⚠️ 发现 {result} 条现有数据，需要手动处理")
            print("   建议：在生产环境中先备份数据")
        else:
            print("✅ 表为空，可以安全进行结构调整")

    except Exception as e:
        print(f"⚠️ 数据检查失败: {e}")

    # ==================== 2. 删除旧约束 ====================
    print("🗑️ 删除旧约束...")

    try:
        # 删除现有的约束
        op.execute(
            "ALTER TABLE visa_status_history DROP CONSTRAINT IF EXISTS simple_visa_status_check"
        )
        print("✅ 删除旧约束 simple_visa_status_check")

    except Exception as e:
        print(f"⚠️ 删除约束失败: {e}")

    # ==================== 3. 删除from_status字段 ====================
    print("🗑️ 删除from_status字段...")

    try:
        # 删除from_status字段（过度设计）
        op.drop_column("visa_status_history", "from_status")
        print("✅ 删除from_status字段")

    except Exception as e:
        print(f"⚠️ 删除from_status字段失败: {e}")

    # ==================== 4. 重命名to_status为visa_status ====================
    print("🔄 重命名to_status为visa_status...")

    try:
        # 重命名字段为更清晰的名称
        op.execute(
            "ALTER TABLE visa_status_history RENAME COLUMN to_status TO visa_status"
        )
        print("✅ 重命名to_status为visa_status")

    except Exception as e:
        print(f"⚠️ 重命名字段失败: {e}")

    # ==================== 5. 添加新的约束 ====================
    print("✅ 添加新的约束...")

    try:
        # 添加新的约束，名称更合理
        op.execute("""
            ALTER TABLE visa_status_history ADD CONSTRAINT visa_status_check
            CHECK (visa_status IN (
                'submit_failure',         -- 提交失败（自动化任务执行失败）
                'submitted',              -- 已提交（自动化成功提交到越南系统）
                'additional_info_required', -- 补充资料（越南领事馆要求补充额外信息）
                'approved',               -- 已批准（越南领事馆批准签证）
                'denied'                  -- 已拒绝（越南领事馆拒绝签证）
            ))
        """)
        print("✅ 添加新约束 visa_status_check")

    except Exception as e:
        print(f"⚠️ 添加约束失败: {e}")

    # ==================== 6. 更新索引 ====================
    print("🔍 更新索引...")

    try:
        # 删除旧的from_status索引
        op.execute("DROP INDEX IF EXISTS ix_visa_status_history_from_status")

        # 删除旧的to_status索引
        op.execute("DROP INDEX IF EXISTS ix_visa_status_history_to_status")
        op.execute("DROP INDEX IF EXISTS ix_visa_status_to_changed")

        # 创建新的visa_status索引
        op.create_index(
            "ix_visa_status_history_visa_status", "visa_status_history", ["visa_status"]
        )

        # 创建复合索引用于查询优化
        op.create_index(
            "ix_visa_status_changed_at",
            "visa_status_history",
            ["visa_status", "changed_at"],
        )

        print("✅ 索引更新完成")

    except Exception as e:
        print(f"⚠️ 索引更新失败: {e}")

    # ==================== 7. 更新字段注释 ====================
    print("📝 更新字段注释...")

    try:
        op.execute(
            "COMMENT ON COLUMN visa_status_history.visa_status IS '签证状态: submit_failure, submitted, additional_info_required, approved, denied'"
        )
        op.execute(
            "COMMENT ON TABLE visa_status_history IS '签证状态历史记录表: 记录签证在官方流程中的状态变化'"
        )
        print("✅ 字段注释更新完成")

    except Exception as e:
        print(f"⚠️ 注释更新失败: {e}")

    print("=" * 60)
    print("✅ visa_status_history表简化完成！")
    print("=" * 60)
    print("📋 完成的改动：")
    print("   ✓ 删除from_status字段（简化设计）")
    print("   ✓ 重命名to_status为visa_status（更清晰）")
    print("   ✓ 重命名约束为visa_status_check（更合理）")
    print("   ✓ 更新相关索引")
    print("   ✓ 更新字段注释")
    print("\n🎯 新的表结构：")
    print("   • visa_status: 单一状态字段，支持5个状态")
    print("   • 简化的设计，适合当前业务需求")
    print("   • 为后续自动同步机制做好准备")


def downgrade():
    """回滚简化操作"""
    print("🔄 回滚visa_status_history表简化...")

    try:
        # 删除新约束
        op.execute(
            "ALTER TABLE visa_status_history DROP CONSTRAINT IF EXISTS visa_status_check"
        )

        # 重命名字段回原名
        op.execute(
            "ALTER TABLE visa_status_history RENAME COLUMN visa_status TO to_status"
        )

        # 添加回from_status字段
        op.add_column(
            "visa_status_history",
            sa.Column(
                "from_status", sa.String(32), nullable=False, server_default="unknown"
            ),
        )

        # 恢复旧约束
        op.execute("""
            ALTER TABLE visa_status_history ADD CONSTRAINT simple_visa_status_check
            CHECK (to_status IN (
                'submit_failure', 'submitted', 'additional_info_required', 'approved', 'denied'
            ))
        """)

        # 恢复旧索引
        op.drop_index("ix_visa_status_history_visa_status", "visa_status_history")
        op.drop_index("ix_visa_status_changed_at", "visa_status_history")

        op.create_index(
            "ix_visa_status_history_from_status", "visa_status_history", ["from_status"]
        )
        op.create_index(
            "ix_visa_status_history_to_status", "visa_status_history", ["to_status"]
        )

        print("✅ 回滚完成")

    except Exception as e:
        print(f"❌ 回滚失败: {e}")
