# 文件位置: app/utils/ocr_utils.py
# 用途：调用阿里云 OCR 接口识别护照信息（使用 APPCODE 简单认证）

import base64
import json
import os

import requests

from app.utils.logger_config import get_logger

logger = get_logger()


# 修改为函数，在运行时获取环境变量
def get_aliyun_appcode():
    appcode = os.environ.get("ALIYUN_APPCODE")
    if not appcode:
        logger.error("❌ 环境变量ALIYUN_APPCODE未设置，OCR功能将无法使用")
    return appcode


# OCR 接口地址
OCR_API_URL = (
    "https://cardpack.market.alicloudapi.com/rest/160601/ocr/ocr_passport.json"
)


def run_aliyun_passport_ocr(image_path: str) -> dict:
    """
    调用阿里云 OCR 接口识别护照图片，返回包含护照字段信息的字典。

    :param image_path: 图片的绝对路径
    :return: 识别结果 dict，如果失败返回空 dict
    """
    logger.info(f"开始调用阿里云 OCR 识别护照: {image_path}")

    # 获取 ALIYUN_APPCODE
    aliyun_appcode = get_aliyun_appcode()

    # 检查API Key是否设置
    if not aliyun_appcode:
        logger.error("❌ 环境变量ALIYUN_APPCODE未设置，无法进行OCR识别")
        return {}

    # ✅ 读取并编码图片为 Base64
    try:
        with open(image_path, "rb") as f:
            image_data = base64.b64encode(f.read()).decode("utf-8")
    except Exception as e:
        logger.error(f"读取图片失败: {e}")
        return {}

    # ✅ 构建请求头（使用 APPCODE 简单认证）
    headers = {
        "Authorization": f"APPCODE {aliyun_appcode}",
        "Content-Type": "application/json; charset=UTF-8",
    }

    # ✅ 构建请求体（Body）
    payload = {"image": image_data}

    # ✅ 发送 POST 请求
    try:
        response = requests.post(OCR_API_URL, headers=headers, data=json.dumps(payload))
        response.raise_for_status()
    except Exception as e:
        logger.error(f"OCR 请求失败: {e}")
        return {}

    # ✅ 解析响应结果
    try:
        result = response.json()
        logger.info(f"OCR 原始响应: {result}")
        if result.get("success"):
            logger.info("✅ OCR 识别成功")
            logger.info(
                "📄 OCR识别完整信息:\n%s",
                json.dumps(result, indent=2, ensure_ascii=False),
            )

            # 格式化 birth_date 字段为 dd/mm/yyyy（直接覆盖原字段）
            dob = result.get("birth_date")
            if dob and len(dob) == 8 and dob.isdigit():
                result["birth_date"] = f"{dob[6:8]}/{dob[4:6]}/{dob[0:4]}"

            return result  # 直接返回原始结构
        else:
            logger.warning("OCR 返回未标记成功")
            logger.warning(
                f"原始返回内容: {json.dumps(result, indent=2, ensure_ascii=False)}"
            )
            return {}

    except Exception as e:
        logger.error(f"解析 OCR 响应失败: {e}")
        return {}
