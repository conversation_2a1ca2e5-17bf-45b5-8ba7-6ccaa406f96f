# 文件: app/utils/captcha_solver.py
"""
Anti-Captcha 服务的封装模块

提供验证码识别功能，通过 Anti-Captcha API 将图片中的验证码识别为文本。
该模块可被应用中的任何组件复用，降低重复代码。

依赖项:
1. requests - HTTP请求库
   安装: pip install requests
"""

import base64
import os
from time import sleep
from typing import Any

from playwright.sync_api import Page
import requests

from app.utils.logger_config import get_logger

logger = get_logger()


class CaptchaSolver:
    """
    验证码求解器，封装 Anti-Captcha API 调用逻辑
    """

    def __init__(
        self, api_key: str | None = None, settings: dict[str, Any] | None = None
    ):
        """
        初始化验证码求解器

        Args:
            api_key: Anti-Captcha API密钥（已废弃，保留参数兼容性）
            settings: 包含配置的字典（已废弃，保留参数兼容性）
        """
        # 只从环境变量获取API密钥
        self.api_key = os.environ.get("ANTI_CAPTCHA_API_KEY")
        if self.api_key:
            logger.info("✅ 从环境变量获取Anti-Captcha API密钥成功")
        else:
            logger.error("❌ 环境变量ANTI_CAPTCHA_API_KEY未设置，验证码识别将失败")

        # 配置参数
        self.max_attempts = 20  # 轮询结果的最大尝试次数
        self.polling_interval = 0.3  # 轮询间隔(秒)
        self.timeout = 20  # API请求超时(秒)

        # 保存上次操作结果信息
        self.last_task_id = None
        self.last_error = None
        self.last_solution = None

    def solve_captcha(self, image_path: str, page: Page) -> str | None:
        """
        识别图片中的验证码

        Args:
            image_path: 验证码图片路径

        Returns:
            str: 识别结果文本，若失败则返回None
        """
        logger.info(f"尝试识别验证码: {image_path}")
        self.last_error = None
        self.last_solution = None

        if not self.api_key:
            logger.error("❌ 未配置Anti-Captcha API密钥，无法使用在线服务")
            return None

        try:
            # 读取图片并转为base64
            with open(image_path, "rb") as image_file:
                image_base64 = base64.b64encode(image_file.read()).decode("utf-8")

            # 准备API请求
            headers = {"Content-Type": "application/json"}
            data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "ImageToTextTask",
                    "body": image_base64,
                    "phrase": False,
                    "case": False,  # 不区分大小写
                    "numeric": 1,  # 仅允许数字
                    "math": False,
                    "minLength": 6,  # 验证码最小长度
                    "maxLength": 6,  # 验证码最大长度
                    "comment": "Verify captcha - 6 digits only",  # 提供额外信息给解析器
                },
                "softId": 0,  # 可替换为软件ID
            }

            # 发送创建任务请求
            logger.info("🔄 创建Anti-Captcha任务...")
            response = requests.post(
                "https://api.anti-captcha.com/createTask",
                json=data,
                headers=headers,
                timeout=self.timeout,
            )
            response_data = response.json()

            if response_data.get("errorId") > 0:
                error_code = response_data.get("errorCode")
                error_desc = response_data.get("errorDescription")
                self.last_error = f"{error_code}: {error_desc}"
                logger.error(f"❌ Anti-Captcha API错误: {self.last_error}")
                return None

            task_id = response_data.get("taskId")
            if not task_id:
                self.last_error = "未能获取任务ID"
                logger.error(f"❌ {self.last_error}")
                return None

            self.last_task_id = task_id
            logger.info(f"✅ Anti-Captcha任务已创建, ID: {task_id}")

            # 轮询结果
            return self._poll_result(task_id, page)

        except Exception as e:
            self.last_error = str(e)
            logger.error(
                f"❌ Anti-Captcha服务调用失败: {self.last_error}", exc_info=True
            )
            return None

    def _poll_result(self, task_id: int | str, page: Page) -> str | None:
        """
        轮询Anti-Captcha的任务结果

        Args:
            task_id: 任务ID

        Returns:
            str: 识别结果，若失败则返回None
        """
        attempt = 0
        check_data = {"clientKey": self.api_key, "taskId": task_id}
        headers = {"Content-Type": "application/json"}

        while attempt < self.max_attempts:
            attempt += 1
            try:
                if page and any(
                    page.is_visible(sel)
                    for sel in [
                        "text=DECLARATION COMPLETED",
                        "text=Electronic document code",
                        "button:has-text('Confirm')",
                    ]
                ):
                    logger.info("✅ 页面已跳转至 DECLARATION，验证码识别填充完成")
                    return None  # 表示跳过本轮识别，主流程继续

            except Exception as e:
                logger.warning(f"⚠️ 页面跳转检测失败: {e}")
            sleep(self.polling_interval)  # 等待一段时间再查询结果

            try:
                logger.info(
                    f"🔄 查询Anti-Captcha结果中... 尝试 {attempt}/{self.max_attempts}"
                )
                check_response = requests.post(
                    "https://api.anti-captcha.com/getTaskResult",
                    json=check_data,
                    headers=headers,
                    timeout=self.timeout,
                )
                result_data = check_response.json()

                if result_data.get("errorId") > 0:
                    error_code = result_data.get("errorCode")
                    error_desc = result_data.get("errorDescription")
                    self.last_error = f"{error_code}: {error_desc}"
                    logger.error(f"❌ 检查结果时出错: {self.last_error}")
                    continue

                if result_data.get("status") == "ready":
                    captcha_text = result_data.get("solution", {}).get("text", "")

                    # 新增 检查识别结果是否符合预期格式6位数字
                    if (
                        captcha_text
                        and len(captcha_text) == 6
                        and captcha_text.isdigit()
                    ):
                        self.last_solution = captcha_text
                        logger.info(
                            f"✅ Anti-Captcha识别成功: {captcha_text}(符合6位数字格式)"
                        )
                        return captcha_text
                    else:
                        self.last_error = (
                            f"API返回的验证码 '{captcha_text}' 不符合6位数字格式"
                        )
                        logger.warning(f"⚠️ {self.last_error},继续等待有效结果...")
                        continue
            except Exception as e:
                self.last_error = str(e)
                logger.error(f"❌ 轮询过程中出错: {self.last_error}")
                continue

        self.last_error = f"达到最大尝试次数 ({self.max_attempts})"
        logger.error(f"❌ Anti-Captcha识别超时: {self.last_error}")
        return None

    def get_last_error(self) -> str | None:
        """获取最后一次操作的错误信息"""
        return self.last_error

    def get_last_solution(self) -> str | None:
        """获取最后一次成功的识别结果"""
        return self.last_solution

    def get_last_task_id(self) -> int | str | None:
        """获取最后一次创建的任务ID"""
        return self.last_task_id


# 便捷函数，用于快速调用
def solve_captcha(
    image_path: str,
    page: Page,
    api_key: str | None = None,
    settings: dict[str, Any] | None = None,
) -> str | None:
    """
    快速识别验证码的便捷函数

    Args:
        image_path: 验证码图片路径
        page: Playwright页面对象
        api_key: 已废弃，保留参数兼容性
        settings: 已废弃，保留参数兼容性

    Returns:
        str: 识别结果，若失败则返回None
    """
    solver = CaptchaSolver()
    return solver.solve_captcha(image_path, page)
