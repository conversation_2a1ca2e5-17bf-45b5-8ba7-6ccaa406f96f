# api/config/settings.py
import os
from pathlib import Path

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings:
    """应用配置 - 简化版本，直接使用环境变量"""

    def __init__(self):
        # 基础配置
        self.app_name = "Vietnam E-Visa Automator API"
        self.app_version = "1.0.0"
        self.debug = os.getenv("DEBUG", "false").lower() == "true"

        # 安全配置
        self.secret_key = os.getenv("SECRET_KEY", "your-very-secret-key")

        # 服务器配置
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))
        self.reload = self.debug

        # 认证配置
        self.auth_cookie_name = os.getenv("AUTH_COOKIE_NAME", "auth_token")
        self.auth_token_expire_hours = int(os.getenv("AUTH_TOKEN_EXPIRE_HOURS", "1"))
        self.max_failed_attempts = int(os.getenv("MAX_FAILED_ATTEMPTS", "10"))
        self.account_lock_hours = int(os.getenv("ACCOUNT_LOCK_HOURS", "100000"))

        # 文件配置
        self.max_file_size = int(
            os.getenv("MAX_FILE_SIZE", str(10 * 1024 * 1024))
        )  # 10MB
        self.allowed_file_types = os.getenv(
            "ALLOWED_FILE_TYPES", "image/jpeg,image/png"
        ).split(",")

        # 日志配置
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_dir = os.getenv("LOG_DIR", "logs/fastapi")
        self.log_filename_prefix = os.getenv("LOG_FILENAME_PREFIX", "vietnam_evisa_api")

        # 数据库配置 - 仅支持PostgreSQL
        self.database_type = os.getenv("DATABASE_TYPE", "postgresql")

        # PostgreSQL配置
        self.postgres_host = os.getenv("POSTGRES_HOST", "localhost")
        self.postgres_port = int(os.getenv("POSTGRES_PORT", "5432"))
        self.postgres_db = os.getenv("POSTGRES_DB", "visa_automator")
        self.postgres_user = os.getenv("POSTGRES_USER", "visa_user")
        self.postgres_password = os.getenv("POSTGRES_PASSWORD", "visa_password_2024")

        # OCR配置
        self.ocr_temp_dir = os.getenv("OCR_TEMP_DIR", "temp/ocr")


class UserConfig:
    """用户配置管理"""

    def __init__(self):
        self.users: dict[str, dict] = {}
        self._load_users_from_env()

    def _load_users_from_env(self):
        """从环境变量加载用户数据"""
        api_users_env = os.getenv("API_BASIC_USERS", "")
        api_passwords_env = os.getenv("API_BASIC_PASSWORDS", "")

        # 安全检查：环境变量必须设置，不提供默认值
        if not api_users_env.strip():
            raise ValueError(
                "API_BASIC_USERS environment variable is required for security"
            )

        if not api_passwords_env.strip():
            raise ValueError(
                "API_BASIC_PASSWORDS environment variable is required for security"
            )

        api_users = api_users_env.split(",")
        api_passwords = api_passwords_env.split(",")

        if len(api_users) != len(api_passwords):
            raise ValueError("Number of users and passwords must match")

        for username, password in zip(api_users, api_passwords, strict=False):
            username = username.strip()
            password = password.strip()
            if username and password:
                self.users[username] = {
                    "password": password,
                    "failed_attempts": 0,
                    "locked_until": None,
                }

    def get_user(self, username: str) -> dict:
        """获取用户信息"""
        return self.users.get(username)

    def update_user(self, username: str, data: dict):
        """更新用户信息"""
        if username in self.users:
            self.users[username].update(data)


# 全局配置实例
settings = Settings()
user_config = UserConfig()

# 路径配置 - 仅保留必要的临时目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent
TEMP_DIR = BASE_DIR / "temp"

# 确保目录存在
TEMP_DIR.mkdir(exist_ok=True)
