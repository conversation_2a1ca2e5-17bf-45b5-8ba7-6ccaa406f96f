# Python相关
venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
*.egg
*.egg-info/
dist/
build/
*.spec

# 数据库文件
*.db
*.db-journal
*.sqlite
*.sqlite3

# 日志文件
*.log
logs/

# 缓存和临时文件
.pytest_cache/
.cache/
.tox/
*.coverage
.coverage
htmlcov/
.mypy_cache/
.dmypy.json
dmypy.json

# IDE和编辑器
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db
.cursor/mcp.json

# 项目特定文件
screenshots/
payment_screenshots/
downloads/
uploads/
temp/
results/
.prefs/
.specstory/
.cursor/
.cursorindexingignore
docs/

# 测试和调试
test_data/
test_applicant/
uid_cache.json
jobs.db
test*.py
check_*.py
simple_*.py
test/

# 敏感信息文件
.env
.env.local
.env.production
.env.staging
*_secret*.json
*_secrets*.json
*_credential*.json
*_credentials*.json
*_key*.json
*_keys*.json
*_card*.json
*_cards*.json
payment_cards.json
app/payment/payment_cards.json
app/email/credentials.json
app/email/token.json
**/credentials*.json
**/token*.json
.env.example

# 特定测试文件
test_payment.py
app/payment_example.py
app/email/gmail_watcher.py
test_expedited_api.py
reorder_applicant_table.py
migrate_to_modular.py
frontend/dist/
frontend/test-results/
test-results/

# 图片和文档文件
error*.png
form*.png
测试*.png
测试*.pdf
*.png
*.jpg
*.jpeg
*.gif
*.pdf
*.doc
*.docx

# Node.js (前端)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 前端构建
static/
static_files/
.vite/
frontend/dist/

# Docker相关
docker-compose.override.yml

# 备份和遗留文件
backup/
legacy_frontend/
reference static/
app/ui/tabs/backup/
backup_before_fixes_20250614_225231/
backup_direct_cleanup_20250614_161006/

# HTML测试文件
test*.html
*_test.html

# 文档和总结文件
*.md
!README.md
!docs/**/*.md

# 其他
FRONTEND_OPTIMIZATION_SUMMARY.md
REFACTOR_SUMMARY.md
china_province_city_map.json
# SpecStory explanation file
.specstory/.what-is-this.md
api/auth/
frontend/playwright-report/
china_province_city_map.json
show_test_datasets.py
switch_browser_mode.py
vietnam_filler_fixes_summary.py
verify_vietnam_filler_fixes.py
create_test_orders.py
1.13.0
create_test_user.py
migrate_user_id_to_uuid.py
create_test_orders_uuid.py
cleanup_test_data.py
frontend/tests/
frontend/test-websocket-implementation.js
frontend/test-websocket-implementation.cjs
frontend/src/components/visa/VisaFileUpload.vue.backup
db/migration_visa_task_to_orders.sql
reports/
scripts/auth_migration_validator.py
auth_migration_validation.json
database_analysis_report.json
scripts/database_connection_analysis.py
backup_visa_automator.sql
emergency_database_cleanup.py
direct_cleanup_plan.py
critical_fixes_implementation.py
cleanup_old_tables.sql
cleanup_report_20250614_161006.txt
alembic/versions/fix_critical_issues.py
openapi.json
backup_before_duplicate_fix_*_*/
database_audit_report_optimized_*.json
database_architecture_audit.py
database_audit_report.json
fix_alembic_version_table.py
migration_backup/
migration_report.json
migrate_email_polling.py
unified_email_service_test_results.json
email_visa_download_test_results.json

coverage.xml
bandit-report.json
.pki/
