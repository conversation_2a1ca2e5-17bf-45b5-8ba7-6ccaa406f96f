import type { StatisticsData } from '@/api/statistics'
import { statisticsApi } from '@/api/statistics'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'

// 可选的配置参数
export interface UseStatisticsOptions {
  autoRefresh?: boolean
  refreshInterval?: number
  defaultPeriod?: string
}

// 错误类型定义
interface ApiError {
  status?: number
  message?: string
}

export const useStatistics = (options: UseStatisticsOptions = {}) => {
  const {
    autoRefresh = false,
    refreshInterval = 30000, // 30秒
    defaultPeriod = '7d',
  } = options

  // 响应式状态
  const loading = ref(false)
  const statistics = ref<StatisticsData | null>(null)
  const currentPeriod = ref(defaultPeriod)
  const lastUpdated = ref<Date | null>(null)
  const refreshTimer = ref<number | null>(null)

  // 计算属性 - 提供安全的默认值
  const basicStats = computed(() => {
    const stats = statistics.value?.basic_stats
    if (!stats) {
      return {
        total_applications: 0,
        successful_applications: 0,
        failed_applications: 0,
        pending_applications: 0,
        success_rate: 0,
      }
    }

    // 确保所有值都是有效数字
    return {
      total_applications: Number(stats.total_applications) || 0,
      successful_applications: Number(stats.successful_applications) || 0,
      failed_applications: Number(stats.failed_applications) || 0,
      pending_applications: Number(stats.pending_applications) || 0,
      success_rate: Number(stats.success_rate) || 0,
    }
  })

  const timeSeriesData = computed(() => statistics.value?.time_series ?? [])

  const statusDistribution = computed(() => statistics.value?.status_distribution ?? [])

  const paymentStats = computed(() => {
    const stats = statistics.value?.payment_stats
    if (!stats) {
      return {
        paid_count: 0,
        pending_count: 0,
        failed_count: 0,
        total_amount: 0,
      }
    }

    // 确保所有值都是有效数字
    return {
      paid_count: Number(stats.paid_count) || 0,
      pending_count: Number(stats.pending_count) || 0,
      failed_count: Number(stats.failed_count) || 0,
      total_amount: Number(stats.total_amount) || 0,
    }
  })

  const visaTypeStats = computed(() => statistics.value?.visa_type_stats ?? [])

  const processingTimeStats = computed(() => {
    const stats = statistics.value?.processing_time_stats
    if (!stats) {
      return {
        average_hours: 0,
        median_hours: 0,
        min_hours: 0,
        max_hours: 0,
        total_processed: 0,
      }
    }

    // 确保所有值都是有效数字
    return {
      average_hours: Number(stats.average_hours) || 0,
      median_hours: Number(stats.median_hours) || 0,
      min_hours: Number(stats.min_hours) || 0,
      max_hours: Number(stats.max_hours) || 0,
      total_processed: Number(stats.total_processed) || 0,
    }
  })

  // 图表数据格式化 - 安全处理
  const chartData = computed(() => {
    const safeTimeSeriesData = Array.isArray(timeSeriesData.value) ? timeSeriesData.value : []
    const safeStatusDistribution = Array.isArray(statusDistribution.value)
      ? statusDistribution.value
      : []
    const safeVisaTypeStats = Array.isArray(visaTypeStats.value) ? visaTypeStats.value : []

    return {
      // 时间趋势数据
      timeSeries: {
        dates: safeTimeSeriesData.map((item) => item?.date || ''),
        applications: safeTimeSeriesData.map((item) => Number(item?.applications) || 0),
        successful: safeTimeSeriesData.map((item) => Number(item?.successful) || 0),
      },

      // 状态分布饼图数据
      statusPie: safeStatusDistribution.map((item) => ({
        name: getStatusLabel(item?.status || ''),
        value: Number(item?.count) || 0,
      })),

      // 签证类型柱状图数据
      visaTypes: {
        categories: safeVisaTypeStats.map((item) => item?.visa_type || ''),
        values: safeVisaTypeStats.map((item) => Number(item?.count) || 0),
      },
    }
  })

  // 获取统计数据
  const fetchStatistics = async (period?: string) => {
    try {
      loading.value = true
      const targetPeriod = period ?? currentPeriod.value

      console.log(`🔍 获取统计数据: period=${targetPeriod}`)

      const response = await statisticsApi.getStatistics(targetPeriod)

      if (response.success && response.data) {
        // 确保数据完整性，提供默认值
        statistics.value = {
          basic_stats: response.data.basic_stats || {
            total_applications: 0,
            successful_applications: 0,
            failed_applications: 0,
            pending_applications: 0,
            success_rate: 0,
          },
          time_series: Array.isArray(response.data.time_series) ? response.data.time_series : [],
          status_distribution: Array.isArray(response.data.status_distribution)
            ? response.data.status_distribution
            : [],
          payment_stats: response.data.payment_stats || {
            paid_count: 0,
            pending_count: 0,
            failed_count: 0,
            total_amount: 0,
          },
          visa_type_stats: Array.isArray(response.data.visa_type_stats)
            ? response.data.visa_type_stats
            : [],
          processing_time_stats: response.data.processing_time_stats || {
            average_hours: 0,
            median_hours: 0,
            min_hours: 0,
            max_hours: 0,
            total_processed: 0,
          },
          period: response.data.period || targetPeriod,
          last_updated: response.data.last_updated || new Date().toISOString(),
        }
        currentPeriod.value = targetPeriod
        lastUpdated.value = new Date()
        console.log('✅ 统计数据获取成功:', statistics.value)
      } else {
        console.warn('⚠️ 统计数据响应异常:', response)
        // 设置默认数据而不是显示错误
        setDefaultStatistics()
        ElMessage.warning('统计数据获取失败，显示默认数据')
      }
    } catch (error: unknown) {
      console.error('❌ 获取统计数据失败:', error)
      const apiError = error as ApiError
      if (apiError.status === 401) {
        ElMessage.error('认证失败，请重新登录')
      } else {
        ElMessage.error('获取统计数据失败: ' + (apiError.message ?? '未知错误'))
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新数据
  const refresh = () => {
    return fetchStatistics()
  }

  // 切换时间周期
  const changePeriod = (period: string) => {
    if (period !== currentPeriod.value) {
      currentPeriod.value = period
      return fetchStatistics(period)
    }
  }

  // 启动自动刷新
  const startAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
    }

    refreshTimer.value = window.setInterval(() => {
      console.log('🔄 自动刷新统计数据')
      fetchStatistics()
    }, refreshInterval)

    console.log(`✅ 自动刷新已启动，间隔: ${refreshInterval / 1000}秒`)
  }

  // 停止自动刷新
  const stopAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
      console.log('⏹️ 自动刷新已停止')
    }
  }

  // 状态标签映射
  const getStatusLabel = (status: string): string => {
    const statusMap: Record<string, string> = {
      SUCCESS: '提交成功',
      FAILED: '提交失败',
      PENDING: '处理中',
      APPROVED: '已审批',
      REJECTED: '已拒绝',
      DOWNLOADED: '已下载',
    }
    return statusMap[status] || status
  }

  // 设置默认统计数据
  const setDefaultStatistics = () => {
    statistics.value = {
      basic_stats: {
        total_applications: 0,
        successful_applications: 0,
        failed_applications: 0,
        pending_applications: 0,
        success_rate: 0,
      },
      time_series: [],
      status_distribution: [],
      payment_stats: {
        paid_count: 0,
        pending_count: 0,
        failed_count: 0,
        total_amount: 0,
      },
      visa_type_stats: [],
      processing_time_stats: {
        average_hours: 0,
        median_hours: 0,
        min_hours: 0,
        max_hours: 0,
        total_processed: 0,
      },
      period: currentPeriod.value,
      last_updated: new Date().toISOString(),
    }
  }

  // 监听自动刷新设置变化
  watch(
    () => autoRefresh,
    (newValue) => {
      if (newValue) {
        startAutoRefresh()
      } else {
        stopAutoRefresh()
      }
    },
    { immediate: true },
  )

  // 组件挂载时获取数据
  onMounted(() => {
    fetchStatistics()
  })

  // 组件卸载时清理定时器
  const cleanup = () => {
    stopAutoRefresh()
  }

  return {
    // 状态
    loading,
    statistics,
    currentPeriod,
    lastUpdated,

    // 计算属性
    basicStats,
    timeSeriesData,
    statusDistribution,
    paymentStats,
    visaTypeStats,
    processingTimeStats,
    chartData,

    // 方法
    fetchStatistics,
    refresh,
    changePeriod,
    startAutoRefresh,
    stopAutoRefresh,
    cleanup,
    getStatusLabel,
  }
}
