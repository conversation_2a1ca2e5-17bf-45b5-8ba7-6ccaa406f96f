#!/usr/bin/env python3
"""
快速代码质量检查
================

专注于实用性的代码检查工具：
- 保持 Ruff 的严格性
- 区分严重错误和风格问题
- 提供清晰的修复建议
"""

import subprocess  # nosec B404 # 开发工具需要subprocess
import sys


def run_ruff_check():
    """运行 Ruff 检查并分析结果"""
    print("🔍 Ruff 代码质量检查")
    print("=" * 50)

    # 运行 Ruff 检查
    result = subprocess.run(  # nosec B603,B607 # 受控的ruff命令执行
        ["ruff", "check", ".", "--output-format", "concise"],
        capture_output=True,
        text=True,
    )

    if result.returncode == 0:
        print("✅ 代码检查通过 - 无错误发现")
        return True

    # 分析错误类型
    lines = result.stdout.strip().split("\n") if result.stdout else []

    critical_errors = []
    style_issues = []

    for line in lines:
        if not line.strip():
            continue

        # 严重错误（会影响代码运行）
        if any(code in line for code in ["F821", "F822", "F831", "E999", "F811"]):
            critical_errors.append(line)
        # 风格问题（不影响运行）
        elif any(code in line for code in ["E402", "F401", "F841", "E722"]):
            style_issues.append(line)
        else:
            # 其他问题归类为风格问题
            style_issues.append(line)

    # 显示结果
    if critical_errors:
        print(f"❌ 发现 {len(critical_errors)} 个严重错误（需要修复）:")
        for error in critical_errors[:5]:  # 只显示前5个
            print(f"   {error}")
        if len(critical_errors) > 5:
            print(f"   ... 还有 {len(critical_errors) - 5} 个错误")
        print()

    if style_issues:
        print(f"⚠️ 发现 {len(style_issues)} 个风格问题（可选修复）:")
        for issue in style_issues[:3]:  # 只显示前3个
            print(f"   {issue}")
        if len(style_issues) > 3:
            print(f"   ... 还有 {len(style_issues) - 3} 个问题")
        print()

    # 提供修复建议
    if critical_errors:
        print("🔧 修复建议:")
        print("   1. 优先修复严重错误（F821, F811 等）")
        print("   2. 运行: ruff check . --fix  # 自动修复部分问题")
        return False
    else:
        print("✅ 无严重错误，风格问题可选择性修复")
        return True


def run_ruff_format():
    """检查代码格式"""
    print("\n🎨 代码格式检查")
    print("=" * 50)

    result = subprocess.run(  # nosec B603,B607 # 受控的ruff命令执行
        ["ruff", "format", "--check", "."], capture_output=True, text=True
    )

    if result.returncode == 0:
        print("✅ 代码格式正确")
        return True
    else:
        print("⚠️ 代码格式需要调整")
        print("🔧 修复命令: ruff format .")
        return False


def main():
    """主函数"""
    print("🚀 快速代码质量检查")
    print("=" * 60)

    # 检查 Ruff 是否安装
    try:
        subprocess.run(["ruff", "--version"], capture_output=True, check=True)  # nosec B603,B607 # 检查ruff版本
    except (FileNotFoundError, subprocess.CalledProcessError):
        print("❌ Ruff 未安装，请运行: pip install ruff")
        sys.exit(1)

    # 运行检查
    code_quality = run_ruff_check()
    code_format = run_ruff_format()

    # 总结
    print("\n" + "=" * 60)
    print("📊 检查结果总结")
    print("=" * 60)

    if code_quality and code_format:
        print("🎉 代码质量良好！")
        print("💡 提示: 风格问题可以选择性修复")
        sys.exit(0)
    elif code_quality and not code_format:
        print("⚠️ 代码逻辑正确，但格式需要调整")
        print("🔧 运行: ruff format . 来修复格式")
        sys.exit(0)  # 格式问题不算失败
    else:
        print("❌ 发现需要修复的严重错误")
        print("🔧 请先修复严重错误，再考虑风格问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
