import uuid

from sqlalchemy import Column, DateTime, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from ..base import Base


class File(Base):
    __tablename__ = "file"
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, doc="文件主键"
    )
    application_id = Column(
        UUID(as_uuid=True),
        ForeignKey("application.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="申请外键",
    )
    file_type = Column(String(32), nullable=False, doc="文件类型")
    file_url = Column(String(256), nullable=False, doc="文件存储URL")
    file_name = Column(String(128), doc="原始文件名")
    uploaded_at = Column(DateTime, server_default=func.now(), doc="上传时间")
    created_at = Column(DateTime, server_default=func.now(), doc="创建时间")
    updated_at = Column(
        DateTime, server_default=func.now(), onupdate=func.now(), doc="更新时间"
    )
