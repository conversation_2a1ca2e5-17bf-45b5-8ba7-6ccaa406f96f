# api/main.py
"""
Vietnam E-Visa Automator API - 纯API后端重构版本

专注于API-only架构，移除所有HTML模板和静态文件依赖
"""

from dataclasses import dataclass

from fastapi import FastAPI, File, UploadFile
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException

# 🔧 关键修复：初始化统一日志系统
from app.utils.logger_config import get_logger, setup_logger

# 使用新的FastAPI Users认证路由
from backend.auth_fastapi_users.routes import router as fastapi_users_auth_router
from backend.config.settings import settings
from backend.core.exceptions import (
    AuthenticationError,
    FileProcessingError,
    OCRProcessingError,
    VisaApplicationError,
    authentication_exception_handler,
    file_processing_exception_handler,
    general_exception_handler,
    http_exception_handler,
    ocr_processing_exception_handler,
    validation_exception_handler,
    visa_application_exception_handler,
)
from backend.middleware.security import (
    RateLimitMiddleware,
    RequestSizeMiddleware,
    SecurityHeadersMiddleware,
)
from backend.routes.automation_logs import router as automation_logs_router
from backend.routes.email_processing import router as email_processing_router
from backend.routes.order import router as order_router  # 新增：订单管理路由
from backend.routes.visa import router as visa_router

# 🔧 关键修复：初始化统一日志系统

# 设置日志系统（上海时区）
setup_logger(
    console_level="INFO", file_level="DEBUG", log_filename_prefix="fastapi_backend"
)
logger = get_logger()


# 移到模块级别以支持APScheduler序列化
@dataclass
class SimpleApplicant:
    email: str
    config: dict
    passport_number: str | None = None


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Vietnam E-Visa Application Automation API - API Only Service",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 添加安全中间件

# CORS 配置 - 支持现代前端开发
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite默认端口
        "http://localhost:5176",  # Vite备用端口
        "http://localhost:3000",  # React/Next.js默认端口
        "http://localhost:8000",  # FastAPI默认端口
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5176",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8000",
        "http://************:8000",  # 生产服务器
    ]
    if settings.debug
    else [
        "http://************:8000"  # 生产环境只允许特定域名
    ],
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],
)

# 生产环境安全中间件
if not settings.debug:
    # 强制 HTTPS（生产环境）
    app.add_middleware(HTTPSRedirectMiddleware)

    # 信任的主机
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=[
            "yourdomain.com",
            "*.yourdomain.com",
            "localhost",
            "127.0.0.1",
            "0.0.0.0",
            "localhost:8000",
            "127.0.0.1:8000",
            "0.0.0.0:8000",
        ],
    )

# 添加安全中间件

# 请求限制中间件
app.add_middleware(RateLimitMiddleware, calls=100, period=60)

# 请求大小限制中间件
app.add_middleware(RequestSizeMiddleware, max_size=settings.max_file_size)

# 安全头中间件
app.add_middleware(SecurityHeadersMiddleware, debug=settings.debug)

# 注册异常处理器
app.add_exception_handler(VisaApplicationError, visa_application_exception_handler)
app.add_exception_handler(OCRProcessingError, ocr_processing_exception_handler)
app.add_exception_handler(FileProcessingError, file_processing_exception_handler)
app.add_exception_handler(AuthenticationError, authentication_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 注册路由
app.include_router(fastapi_users_auth_router)
app.include_router(visa_router, prefix="/api")
app.include_router(order_router, prefix="/api")  # 新增：注册订单路由

# 注册 automation_logs 路由
app.include_router(automation_logs_router)

# 注册邮件处理路由
app.include_router(email_processing_router, prefix="/api")

# 注意：visa_router已经在上面注册，不要重复注册


@app.get("/")
async def api_info():
    """
    API服务信息 - 根路径
    """
    return {
        "service": "Vietnam E-Visa Automator API",
        "version": settings.app_version,
        "status": "running",
        "api_docs": "/docs",
        "message": "API-only service",
    }


# 兼容旧API的OCR端点
@app.post("/ocr-passport/", tags=["OCR"])
async def ocr_passport_legacy_endpoint(passport_scan: UploadFile = File(...)):
    """
    护照OCR识别 (兼容旧API)

    这个端点与旧API保持完全一致，直接返回OCR字段而不包装在response对象中
    """
    from datetime import datetime
    from pathlib import Path
    import uuid

    from app.data.applicant_mapper import normalize_ocr_result_for_applicant
    from app.utils.ocr_utils import run_aliyun_passport_ocr

    # 生成UUID和北京时间戳的文件名
    file_uuid = str(uuid.uuid4())
    beijing_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    original_filename = passport_scan.filename or "passport.jpg"
    temp_filename = f"{file_uuid}_{beijing_time}_{original_filename}"

    # 使用/tmp目录（跨平台兼容）
    temp_dir = Path("/tmp") if Path("/tmp").exists() else Path.cwd() / "temp"
    temp_dir.mkdir(exist_ok=True)
    temp_passport_path = temp_dir / temp_filename

    try:
        # 写入临时文件
        with open(temp_passport_path, "wb") as temp_file:
            content = await passport_scan.read()
            temp_file.write(content)

        # 执行OCR处理
        ocr_result = run_aliyun_passport_ocr(str(temp_passport_path))
        fields = normalize_ocr_result_for_applicant(ocr_result) if ocr_result else {}
        return fields  # 直接返回字段，与旧API保持一致

    except Exception as e:
        print(f"❌ OCR处理失败 (北京时间: {beijing_time}): {e}")
        raise
    finally:
        # 强制清理临时文件
        try:
            if temp_passport_path.exists():
                temp_passport_path.unlink()
                print(f"✅ 临时文件已清理: {temp_filename}")
        except Exception as cleanup_error:
            print(f"⚠️ 清理临时文件失败 (北京时间: {beijing_time}): {cleanup_error}")


# 全局引擎实例 - 参考旧API的做法
try:
    from app.core.visa_automation_engine import VisaAutomationEngine

    global_engine = VisaAutomationEngine()
    print("✅ Global VisaAutomationEngine initialized successfully.")
except Exception as e:
    print(f"❌ Failed to initialize global VisaAutomationEngine: {e}")
    global_engine = None


# 应用生命周期事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    # ✅ 日志系统已在模块级别初始化，使用上海时区
    logger.info(f"🚀 {settings.app_name} v{settings.app_version} 启动成功")
    logger.info(f"📊 调试模式: {'开启' if settings.debug else '关闭'}")
    logger.info("🔧 架构模式: API-only (无模板/静态文件)")
    logger.info("🕐 统一日志系统已配置（上海时区）")

    # 统一数据库初始化，只调用一次
    try:
        from backend.db_config.initializer import DatabaseInitializer
        from backend.db_config.unified_connection import get_unified_db

        print("⚙️ 正在初始化统一数据库（包含用户表、订单表、申请表等）...")
        unified_db = await get_unified_db()
        # 执行一次性数据库初始化(包含所有表结构)
        success = await DatabaseInitializer.initialize_once(unified_db.engine)
        if success:
            print("✅ 统一数据库初始化完成（包含所有表结构）")
        else:
            raise Exception("数据库初始化失败")

    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        print("🔧 解决方案:")
        print("   1. 确保Docker已启动: docker-compose up -d postgres")
        print("   2. 检查连接配置: POSTGRES_HOST=127.0.0.1")
        print("   3. 验证端口: netstat -an | findstr :5432")
        print("🚨 应用将无法正常工作，请检查数据库配置")
        # 抛出异常，阻止应用启动
        raise e

    # 🔥 邮件轮询已移至独立服务
    # 邮件轮询现在由独立的 email-polling 容器处理
    # 避免在容器扩展时重复轮询同一邮箱
    print("📧 邮件轮询由独立服务处理，主应用不再初始化邮件调度器")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print(f"🛑 {settings.app_name} 正在关闭...")

    # 🔥 邮件轮询已移至独立服务，无需在主应用中关闭
    print("📧 邮件轮询由独立服务管理，主应用无需关闭邮件组件")

    # 关闭自动化引擎
    global global_engine
    if global_engine:
        try:
            global_engine.cleanup()
            print("✅ 自动化引擎已关闭")
        except Exception as e:
            print(f"⚠️ 关闭自动化引擎时出错: {e}")

    print(f"👋 {settings.app_name} 已关闭")


# 注意：签证申请端点在 api/routes/visa.py 中定义
# 路径为 /api/visa/apply (通过 visa_router + prefix="/api")


# 开发环境启动
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "api.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
