import type { AxiosResponse } from 'axios'
import axios from 'axios'
import type {
  ApiResponse,
  CreateOrderRequest,
  CreateOrderResponse,
  OrderDetailResponse,
  OrderQueryResponse,
  OrderStatus,
  UpdateOrderRequest,
} from './types'

// 定义错误处理类型
interface AxiosError {
  response?: {
    status?: number
    data?: {
      detail?: string
      message?: string
    }
  }
  message?: string
}

// 订单查询参数
export interface OrderQueryParams {
  page?: number
  limit?: number
  status?: OrderStatus
  order_no?: string
  application_number?: string
  date_from?: string
  date_to?: string
}

/**
 * 订单管理API类
 * 实现订单的创建、查询、更新、状态管理等功能
 */
export class OrderAPI {
  private static readonly BASE_URL = '/api/visa/orders'

  /**
   * 创建新订单
   * 实现幂等性：同一用户+姓名+护照号+出生日期只能创建一个订单
   */
  static async createOrder(request: CreateOrderRequest): Promise<CreateOrderResponse> {
    try {
      const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')

      const response: AxiosResponse<CreateOrderResponse> = await axios.post(
        `${this.BASE_URL}/create`,
        request,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: token ? `Bearer ${token}` : '',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('创建订单失败:', error)

      const axiosError = error as AxiosError

      // 处理认证错误
      if (axiosError.response?.status === 401) {
        throw {
          success: false,
          message: '用户未登录，请先登录',
          error: 'Unauthorized',
        }
      }

      throw {
        success: false,
        message:
          axiosError.response?.data?.detail ??
          axiosError.response?.data?.message ??
          '创建订单失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 查询订单列表
   * 支持多条件筛选和分页
   */
  static async queryOrders(params: OrderQueryParams = {}): Promise<OrderQueryResponse> {
    try {
      const queryParams = new URLSearchParams()

      // 添加查询参数
      queryParams.append('page', String(params.page ?? 1))
      queryParams.append('limit', String(params.limit ?? 20))

      if (params.status) queryParams.append('status', params.status)
      if (params.order_no) queryParams.append('order_no', params.order_no)
      if (params.application_number)
        queryParams.append('application_number', params.application_number)
      if (params.date_from) queryParams.append('date_from', params.date_from)
      if (params.date_to) queryParams.append('date_to', params.date_to)

      const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')

      const response: AxiosResponse<OrderQueryResponse> = await axios.get(
        `${this.BASE_URL}/query?${queryParams.toString()}`,
        {
          headers: {
            Authorization: token ? `Bearer ${token}` : '',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('查询订单失败:', error)

      const axiosError = error as AxiosError

      // 处理认证错误
      if (axiosError.response?.status === 401) {
        throw {
          success: false,
          message: '用户未登录，请先登录',
          error: 'Unauthorized',
        }
      }

      throw {
        success: false,
        message:
          axiosError.response?.data?.detail ??
          axiosError.response?.data?.message ??
          '查询订单失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 获取订单详情
   * 包含完整订单信息和状态变更历史
   */
  static async getOrderDetail(orderNo: string): Promise<OrderDetailResponse> {
    try {
      const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')

      const response: AxiosResponse<OrderDetailResponse> = await axios.get(
        `${this.BASE_URL}/${orderNo}/detail`,
        {
          headers: {
            Authorization: token ? `Bearer ${token}` : '',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('获取订单详情失败:', error)

      const axiosError = error as AxiosError

      // 处理认证错误
      if (axiosError.response?.status === 401) {
        throw {
          success: false,
          message: '用户未登录，请先登录',
          error: 'Unauthorized',
        }
      }

      throw {
        success: false,
        message: axiosError.response?.data?.message ?? '获取订单详情失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 更新订单信息
   * 主要用于更新越南官方编号、状态等
   */
  static async updateOrder(orderNo: string, request: UpdateOrderRequest): Promise<ApiResponse> {
    try {
      const response: AxiosResponse<ApiResponse> = await axios.put(
        `${this.BASE_URL}/${orderNo}/update`,
        request,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('更新订单失败:', error)

      const axiosError = error as AxiosError

      throw {
        success: false,
        message: axiosError.response?.data?.message ?? '更新订单失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 重试订单处理
   * 用于失败订单的重新处理
   */
  static async retryOrder(orderNo: string): Promise<ApiResponse> {
    try {
      const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')

      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${this.BASE_URL}/${orderNo}/retry`,
        {},
        {
          headers: {
            Authorization: token ? `Bearer ${token}` : '',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('重试订单失败:', error)

      const axiosError = error as AxiosError

      // 处理认证错误
      if (axiosError.response?.status === 401) {
        throw {
          success: false,
          message: '用户未登录，请先登录',
          error: 'Unauthorized',
        }
      }

      throw {
        success: false,
        message: axiosError.response?.data?.message ?? '重试订单失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 根据越南官方编号查询订单
   * 用于兼容性查询
   */
  static async getOrderByApplicationNumber(
    applicationNumber: string,
  ): Promise<OrderDetailResponse> {
    try {
      const response: AxiosResponse<OrderDetailResponse> = await axios.get(
        `${this.BASE_URL}/by-application/${applicationNumber}`,
      )
      return response.data
    } catch (error: unknown) {
      console.error('根据申请编号查询订单失败:', error)

      const axiosError = error as AxiosError

      throw {
        success: false,
        message: axiosError.response?.data?.message ?? '查询订单失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 获取用户的所有订单
   * 按创建时间倒序排列
   */
  static async getUserOrders(
    params: { page?: number; limit?: number } = {},
  ): Promise<OrderQueryResponse> {
    return this.queryOrders({
      page: params.page ?? 1,
      limit: params.limit ?? 20,
    })
  }

  /**
   * 获取订单状态历史
   */
  static async getOrderStatusHistory(orderNo: string): Promise<ApiResponse> {
    try {
      const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')

      const response: AxiosResponse<ApiResponse> = await axios.get(
        `${this.BASE_URL}/${orderNo}/history`,
        {
          headers: {
            Authorization: token ? `Bearer ${token}` : '',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('获取订单状态历史失败:', error)

      const axiosError = error as AxiosError

      // 处理认证错误
      if (axiosError.response?.status === 401) {
        throw {
          success: false,
          message: '用户未登录，请先登录',
          error: 'Unauthorized',
        }
      }

      throw {
        success: false,
        message: axiosError.response?.data?.message ?? '获取状态历史失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 获取订单统计信息
   */
  static async getOrderStats(): Promise<ApiResponse> {
    try {
      const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')

      const response: AxiosResponse<ApiResponse> = await axios.get(
        `${this.BASE_URL}/stats/summary`,
        {
          headers: {
            Authorization: token ? `Bearer ${token}` : '',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('获取订单统计失败:', error)

      const axiosError = error as AxiosError

      // 处理认证错误
      if (axiosError.response?.status === 401) {
        throw {
          success: false,
          message: '用户未登录，请先登录',
          error: 'Unauthorized',
        }
      }

      throw {
        success: false,
        message:
          axiosError.response?.data?.detail ??
          axiosError.response?.data?.message ??
          '获取统计信息失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }

  /**
   * 取消订单
   * 仅限created状态的订单可以取消
   */
  static async cancelOrder(orderNo: string, reason?: string): Promise<ApiResponse> {
    try {
      const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')

      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${this.BASE_URL}/${orderNo}/cancel`,
        { reason },
        {
          headers: {
            Authorization: token ? `Bearer ${token}` : '',
          },
        },
      )
      return response.data
    } catch (error: unknown) {
      console.error('取消订单失败:', error)

      const axiosError = error as AxiosError

      // 处理认证错误
      if (axiosError.response?.status === 401) {
        throw {
          success: false,
          message: '用户未登录，请先登录',
          error: 'Unauthorized',
        }
      }

      throw {
        success: false,
        message: axiosError.response?.data?.message ?? '取消订单失败，请稍后重试',
        error: axiosError.message ?? '未知错误',
      }
    }
  }
}

export default OrderAPI
