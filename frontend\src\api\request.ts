import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import type { ApiResponse, ApiError } from './types'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/', // 使用相对路径，由nginx代理处理
  timeout: 30000, // 30秒超时，对应旧版长时间处理需求
  withCredentials: true, // 确保发送cookies，严格对应旧版会话管理
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加JWT认证支持
request.interceptors.request.use(
  (config) => {
    console.log('🌐 发送API请求:', config.method?.toUpperCase(), config.url)

    // 自动添加JWT token到请求头
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      console.log('🔐 已添加JWT token到请求头')
    } else {
      console.log('⚠️ 未找到auth_token，请求将不包含认证头')
    }

    return config
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器，严格对应旧版错误处理逻辑
request.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('✅ API响应成功:', response.config.url, response.status)
    return response
  },
  (error) => {
    console.error('❌ API响应错误:', error.config?.url, error.response?.status)

    // 处理会话过期，严格对应旧版401处理逻辑
    if (error.response?.status === 401) {
      console.log('❌ 检测到认证失败 (401) - 可能是token过期或无效')

      // 清除无效的token
      localStorage.removeItem('auth_token')

      ElMessage.error('认证失败，请重新登录。')

      // 立即重定向到登录页面
      window.location.href = '/login'

      return Promise.reject({
        status: 401,
        message: 'UNAUTHORIZED',
        details: error.response?.data,
      } as ApiError)
    }

    // 处理服务器错误，严格对应旧版错误分类
    if (error.response?.status === 500) {
      ElMessage.error('服务器暂时无法处理请求，请稍后重试')
      return Promise.reject({
        status: 500,
        message: 'Internal Server Error',
        details: error.response?.data,
      } as ApiError)
    }

    if (error.response?.status === 403) {
      ElMessage.error('权限不足，请联系管理员')
      return Promise.reject({
        status: 403,
        message: 'Forbidden',
        details: error.response?.data,
      } as ApiError)
    }

    // 处理网络错误
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      //ElMessage.error('请求超时，请检查网络连接后重试')
      const timeoutError: ApiError = {
        status: 0,
        message: 'Request Timeout',
        details: error.message,
      }
      return Promise.reject(new Error(JSON.stringify(timeoutError)))
    }

    if (error.code === 'NETWORK_ERROR' || !error.response) {
      ElMessage.error('网络连接失败，请检查网络后重试')
      const networkError: ApiError = {
        status: 0,
        message: 'Network Error',
        details: error.message,
      }
      return Promise.reject(new Error(JSON.stringify(networkError)))
    }

    // 通用错误处理
    const errorMessage = error.response?.data?.message ?? error.message ?? '请求失败'
    ElMessage.error(errorMessage)

    const generalError: ApiError = {
      status: error.response?.status ?? 0,
      message: errorMessage,
      details: error.response?.data,
    }
    return Promise.reject(generalError)
  },
)

// 封装常用请求方法，提供类型安全
export const api = {
  // GET请求
  get: <T = unknown>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return request.get(url, config).then((response) => response.data)
  },

  // POST请求
  post: <T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> => {
    return request.post(url, data, config).then((response) => response.data)
  },

  // PUT请求
  put: <T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> => {
    return request.put(url, data, config).then((response) => response.data)
  },

  // DELETE请求
  delete: <T = unknown>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return request.delete(url, config).then((response) => response.data)
  },

  // 文件上传专用POST方法
  upload: <T = unknown>(
    url: string,
    formData: FormData,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> => {
    return request
      .post(url, formData, {
        ...config,
        headers: {
          'Content-Type': 'multipart/form-data',
          ...config?.headers,
        },
      })
      .then((response) => response.data)
  },
}
