import json
import os
from pathlib import Path
import secrets

from app.utils.logger_config import get_logger

logger = get_logger()


# 加载 china_province_city_map.json 映射
CHINA_CITY_MAP_PATH = (
    Path(__file__).resolve().parent / ".." / "data" / "china_province_city_map.json"
)
with open(CHINA_CITY_MAP_PATH, encoding="utf-8") as f:
    CHINA_PROVINCE_CITY_MAP = json.load(f)


'''
# 加载 zh_to_en_province_map.json 映射表
ZH_TO_EN_PATH = Path(__file__).resolve().parent / ".." / "data" / "zh_to_en_province_map.json"
with open(ZH_TO_EN_PATH, "r", encoding="utf-8") as f:
    ZH_TO_EN_PROVINCE_MAP = json.load(f)

def convert_province_to_pinyin(chinese_province: str) -> str:
    """
    将中文省份名转换为拼音（大写）。
    例如：“江苏” -> “JIANGSU”，如果无法匹配则返回空字符串。
    """
    province = chinese_province.strip()
    pinyin = ZH_TO_EN_PROVINCE_MAP.get(province, "")
    if not pinyin:
        logger.warning(f"⚠️ 无法将出生地 '{province}' 转换为拼音")
    return pinyin
'''
# 加载 china_province_city_map.json 映射表
JSON_PATH = os.path.join(
    os.path.dirname(__file__), "..", "data", "china_province_city_map.json"
)
with open(JSON_PATH, encoding="utf-8") as f:
    CHINA_PROVINCE_CITY_MAP = json.load(f)


def generate_random_address(place_of_birth: str) -> str:
    """
    根据出生地拼音生成英文格式的地址
    支持3种区域格式：
    1. 港澳+直辖市：        {city}, {province}, China
    2. 5个自治区：           {city} City, {province}, China
    3. 其他常规省份：        {city} City, {province} Province, China
    """
    place_of_birth = place_of_birth.strip()
    province_pinyin = place_of_birth.strip().upper()

    if not province_pinyin:
        logger.warning(f"⚠️ 无法将出生地 '{place_of_birth}' 转换为拼音")
        province_pinyin = "GUANGDONG"  # 兜底默认值

    city_list = CHINA_PROVINCE_CITY_MAP.get(province_pinyin, [])
    if not city_list:
        logger.warning(
            f"⚠️ OCR 提取的出生地无效或未匹配: '{province_pinyin}'，将使用默认省份 'GUANGDONG'"
        )
        province_pinyin = "GUANGDONG"
        city_list = CHINA_PROVINCE_CITY_MAP.get(province_pinyin, [])

    if not city_list:
        logger.error("❌ 无法找到可用的省份生成随机地址")
        raise ValueError("无法找到可用的省份生成随机地址")

    city = secrets.choice(city_list)

    # 分类区域定义
    REGION_DIRECT = {
        "MACAO",
        "HONG KONG",
        "BEIJING",
        "TIANJIN",
        "SHANGHAI",
        "CHONGQING",
    }
    REGION_AUTONOMOUS = {
        "XINJIANG UYGUR AUTONOMOUS REGION",
        "NINGXIA HUI AUTONOMOUS REGION",
        "TIBET AUTONOMOUS REGION",
        "GUANGXI",
        "NEI MONGOL",
    }

    if province_pinyin in REGION_DIRECT:
        address = f"{city}, {province_pinyin.title()}, China"
    elif province_pinyin in REGION_AUTONOMOUS:
        address = f"{city} City, {province_pinyin.title()}, China"
    else:
        address = f"{city} City, {province_pinyin.title()} Province, China"

    logger.info(f"Generated random address: {address}")
    return address
