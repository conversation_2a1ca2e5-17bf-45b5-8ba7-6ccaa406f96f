{"editor.selectionHighlight": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "editor.rulers": [88], "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "python.defaultInterpreterPath": "./venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.testing.pytestArgs": ["app", "tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.testing.autoTestDiscoverOnSaveEnabled": true, "python.linting.enabled": true, "python.linting.ruffEnabled": true, "python.formatting.provider": "ruff", "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "editor.tabSize": 4, "editor.insertSpaces": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "on"}, "files.associations": {"*.env.example": "properties", "Dockerfile*": "dockerfile", "docker-compose*.yml": "yaml"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/venv": true, "**/node_modules": true, "**/.pytest_cache": true, "**/screenshots": true, "**/downloads": true, "**/temp": true}, "mcp": {"inputs": [], "servers": {"mcp-server-time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=Asia/Shanghai"], "env": {}}}}, "chat.mcp.discovery.enabled": true}