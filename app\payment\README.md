# 越南电子签证支付模块

这个模块负责越南电子签证申请系统的自动支付流程，支持使用多张信用卡进行随机选择付款。

## 功能特点

- 自动检测并使用本地浏览器 (优先顺序: Edge → Chrome → Firefox)
- 支持多张信用卡配置，随机选择一张进行支付
- 自动填写支付表单，包括卡号、持卡人信息、账单地址等
- 提供多种容错机制，确保支付流程稳定
- 自动截图记录关键步骤，便于调试
- 支持与表单填写流程整合，实现完整的自动化处理

## 目录结构

```
app/payment/
├── __init__.py              # 包初始化文件
├── payment_automation.py    # 核心支付自动化实现
├── payment_models.py        # 数据模型定义
├── payment_integration.py   # 与表单填写流程整合
├── payment_system.py        # 支付系统入口
├── payment_cards.json       # 信用卡配置文件
└── README.md                # 本说明文件
```

## 使用方法

### 1. 仅使用支付功能

```python
from app.payment import execute_payment

# 使用默认配置文件中的信用卡信息
success = execute_payment("https://example.com/payment")

# 或指定配置文件
success = execute_payment("https://example.com/payment", "path/to/cards.json")
```

### 2. 自定义信用卡信息

```python
from app.payment import CreditCardInfo, create_credit_card, process_payment

# 创建信用卡对象
cards = [
    create_credit_card(
        card_type="VISA",
        card_number="****************",
        first_name="John",
        last_name="Doe",
        billing_address="123 Test Street",
        city="Beijing",
        country="China",
        exp_month="12",
        exp_year="2030",
        cvv="123"
    )
]

# 自定义设置
settings = {
    "payment_timeout_ms": 30000,
    "screenshots_dir": "payment_screenshots"
}

# 执行支付流程
success = process_payment("https://example.com/payment", cards, settings)
```

### 3. 与表单填写流程整合

```python
from app.payment import integrate_and_execute
from app.fillers.vietnam_filler import VietnamFiller

# 创建填写器并完成必要配置
filler = VietnamFiller()
filler.prepare(locators, settings)

# 运行完整流程
success = integrate_and_execute(
    filler=filler,
    fill_function=filler.fill_step1_personal_info,
    applicant=applicant
)
```

## 配置文件格式

```json
{
    "settings": {
        "payment_timeout_ms": 30000,
        "screenshots_dir": "payment_screenshots",
        "prefer_browser": "edge",
        "headless": false,
        "retry_count": 2
    },
    "credit_cards": [
        {
            "card_type": "VISA",
            "card_number": "****************",
            "first_name": "John",
            "last_name": "Doe",
            "billing_address": "123 Test Street",
            "city": "Beijing",
            "country": "China",
            "exp_month": "12",
            "exp_year": "2030",
            "cvv": "123",
            "note": "主卡"
        }
    ]
}
```

## 注意事项

1. 首次运行时，如果未找到配置文件，将创建示例配置
2. 请确保配置文件中的信用卡信息有效
3. 支付过程中会自动截图，保存在 `payment_screenshots` 目录
4. 支持的卡类型: `VISA`, `MASTERCARD`, `JCB`, `AMEX` 
