/**
 * BusinessStore单元测试
 * ====================
 *
 * 测试新的统一业务状态管理Store
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useBusinessStore } from '@/stores/business'

// Mock API
vi.mock('@/api/request', () => ({
  api: {
    get: vi.fn(),
  },
}))

describe('BusinessStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    // 清理localStorage
    localStorage.clear()
  })

  it('should initialize with idle state', () => {
    const store = useBusinessStore()

    expect(store.isIdle).toBe(true)
    expect(store.isSubmitting).toBe(false)
    expect(store.currentSubmittingPassport).toBe(null)
    expect(store.businessItems).toEqual([])
  })

  it('should start submission correctly', () => {
    const store = useBusinessStore()
    const passportNumber = '*********'

    store.startSubmission(passportNumber)

    expect(store.isSubmitting).toBe(true)
    expect(store.isIdle).toBe(false)
    expect(store.currentSubmittingPassport).toBe(passportNumber)
  })

  it('should prevent duplicate submission', () => {
    const store = useBusinessStore()
    const passportNumber = '*********'

    store.startSubmission(passportNumber)

    expect(() => {
      store.startSubmission(passportNumber)
    }).toThrow('申请正在提交中，请勿重复点击提交按钮。')
  })

  it('should add business item correctly', () => {
    const store = useBusinessStore()
    const businessItem = {
      passportNumber: '*********',
      applicantName: 'Zhang Wei',
      chineseName: '张伟',
      submissionTime: '2025-06-21 10:00:00',
      orderStatus: 'created' as const,
      orderNo: 'VN20250621TEST001',
      automationStatus: 'processing' as const,
    }

    store.addBusinessItem(businessItem)

    expect(store.businessItems).toHaveLength(1)
    expect(store.businessItems[0]).toMatchObject(businessItem)
    expect(store.hasBusinessItems).toBe(true)
  })

  it('should update business item status', () => {
    const store = useBusinessStore()
    const passportNumber = '*********'

    store.addBusinessItem({
      passportNumber,
      applicantName: 'Zhang Wei',
      submissionTime: '2025-06-21 10:00:00',
      orderStatus: 'created' as const,
      automationStatus: 'processing' as const,
    })

    store.updateBusinessItemStatus(passportNumber, 'success', {
      visaStatus: 'approved',
    })

    const item = store.businessItems[0]
    expect(item.automationStatus).toBe('success')
    expect(item.visaStatus).toBe('approved')
  })

  it('should calculate statistics correctly', () => {
    const store = useBusinessStore()

    // 添加不同状态的业务项目
    const items = [
      {
        passportNumber: 'E1',
        applicantName: 'A1',
        submissionTime: '2025-06-21',
        orderStatus: 'created' as const,
        automationStatus: 'processing' as const,
      },
      {
        passportNumber: 'E2',
        applicantName: 'A2',
        submissionTime: '2025-06-21',
        orderStatus: 'created' as const,
        automationStatus: 'success' as const,
      },
      {
        passportNumber: 'E3',
        applicantName: 'A3',
        submissionTime: '2025-06-21',
        orderStatus: 'created' as const,
        automationStatus: 'failed' as const,
      },
    ]

    items.forEach((item) => store.addBusinessItem(item))

    const stats = store.businessStats
    expect(stats.total).toBe(3)
    expect(stats.processing).toBe(1)
    expect(stats.success).toBe(1)
    expect(stats.failed).toBe(1)
  })
})

// 其他store的测试将在迁移完成后添加
