"""
数据库统一初始化管理器
===============================
解决重复建表问题，提供幂等性保
"""

import asyncio
import logging
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncEngine

from app.data.base import Base

logger = logging.getLogger(__name__)


class DatabaseInitializer:
    """数据库初始化单例管理"""

    _instance: Optional["DatabaseInitializer"] = None
    _initialized: bool = False
    _lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def initialize_once(cls, engine: AsyncEngine) -> bool:
        """
        统一的数据库初始化入

        Args:
            engine: SQLAlchemy异步引擎

        Returns:
            bool: 初始化是否成
        """
        # instance = cls()  # 未使用，已注释

        async with cls._lock:
            if cls._initialized:
                logger.info("🔄 数据库已初始化，跳过重复初始化")
                print("🔄 数据库已初始化，跳过重复初始化")
                return True

            try:
                logger.info("🔄 开始数据库初始化")
                # 使用SQLAlchemy自动建表
                async with engine.begin() as conn:
                    await conn.run_sync(Base.metadata.create_all)
                    logger.info("🔄 数据库表结构创建完成")
                    print("🔄 数据库表结构创建完成")

                cls._initialized = True
                logger.info("🔄 数据库初始化完成")
                return True

            except Exception as e:
                logger.error(f"🔄 数据库初始化失败: {e}")
                return False

    @classmethod
    def is_initialized(cls) -> bool:
        """检查是否已初始化"""
        return cls._initialized

    @classmethod
    def reset(cls):
        """重置初始化状态，仅用于测试"""
        cls._initialized = False
        print("重置初始化状态")
