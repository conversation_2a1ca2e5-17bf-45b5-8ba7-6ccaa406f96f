"""
统一邮件轮询服务
================

整合所有邮件轮询相关功能到一个统一的服务类中，消除代码重复和架构混乱

功能：
- 邮件轮询调度管理
- 邮件解析和处理
- 配置管理
- 错误处理和重试
- 资源管理
"""

from concurrent.futures import ThreadPoolExecutor
import contextlib
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime
import email
from email.utils import parseaddr
import json
from pathlib import Path
import random
import ssl
import threading
from zoneinfo import ZoneInfo

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from imapclient import IMAPClient

from app.email.unified_email_parser import UnifiedEmailParser
from app.email.unified_email_processor import UnifiedEmailProcessor
from app.utils.env_loader import load_email_accounts_from_env
from app.utils.logger_config import get_logger

logger = get_logger()


@dataclass
class EmailAccount:
    """邮箱账户配置"""

    email: str
    host: str
    port: int
    password: str
    allowed_senders: list[str]


@dataclass
class EmailMessage:
    """邮件消息"""

    uid: int
    sender: str
    subject: str
    body: str
    received_time: datetime
    raw: email.message.Message


class EmailPollingConfig:
    """邮件轮询配置"""

    # 活动时间窗口（小时）
    ACTIVE_WINDOWS = [
        (9, 13),  # 上午9点到下午1点
        (16, 23),  # 下午4点到晚上11点
    ]

    # 重试配置
    MAX_RETRIES = 3
    RETRY_BACKOFF = 2

    # 线程池配置
    MAX_WORKERS = 10

    # 时区配置
    TIMEZONE = ZoneInfo("Asia/Shanghai")


class UnifiedEmailService:
    """统一邮件轮询服务"""

    def __init__(self):
        self.config = EmailPollingConfig()
        self.accounts: dict[str, EmailAccount] = {}
        self.uid_cache: dict[str, int] = {}
        self.scheduler: BackgroundScheduler | None = None
        self.thread_pool: ThreadPoolExecutor | None = None
        self._lock = threading.Lock()
        self._running = False

        # 初始化组件
        self.parser = UnifiedEmailParser()
        self.processor = UnifiedEmailProcessor()

        # 初始化
        self._load_configuration()
        self._load_uid_cache()

    def _load_configuration(self):
        """加载邮箱配置"""
        try:
            logger.info("🔍 加载邮箱配置...")

            email_config = load_email_accounts_from_env()
            email_accounts_map = email_config.get("email_accounts", {})
            allowed_senders_map = email_config.get("allowed_senders", {})

            for email_addr, config_dict in email_accounts_map.items():
                if not isinstance(config_dict, dict):
                    continue

                account = EmailAccount(
                    email=email_addr,
                    host=config_dict.get("host", ""),
                    port=int(config_dict.get("port", 993)),
                    password=config_dict.get("password", ""),
                    allowed_senders=allowed_senders_map.get(email_addr, []),
                )

                self.accounts[email_addr] = account
                logger.info(f"✅ 加载邮箱账户: {email_addr}")

            logger.info(f"📊 总共加载 {len(self.accounts)} 个邮箱账户")

        except Exception as e:
            logger.error(f"❌ 加载邮箱配置失败: {e}", exc_info=True)

    def _load_uid_cache(self):
        """加载UID缓存"""
        try:
            cache_file = Path("uid_cache.json")
            if cache_file.exists():
                with open(cache_file, encoding="utf-8") as f:
                    self.uid_cache = json.load(f)
                logger.debug(f"✅ 加载UID缓存: {len(self.uid_cache)} 个账户")
            else:
                self.uid_cache = {}
                logger.debug("📝 创建新的UID缓存")
        except Exception as e:
            logger.warning(f"⚠️ 加载UID缓存失败: {e}")
            self.uid_cache = {}

    def _save_uid_cache(self):
        """保存UID缓存"""
        try:
            cache_file = Path("uid_cache.json")
            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump(self.uid_cache, f, indent=2)
            logger.debug("✅ UID缓存已保存")
        except Exception as e:
            logger.warning(f"⚠️ 保存UID缓存失败: {e}")

    def is_within_active_window(self) -> bool:
        """检查当前时间是否在活动窗口内"""
        now = datetime.now().time()
        return any(start <= now.hour < end for start, end in self.config.ACTIVE_WINDOWS)

    @contextmanager
    def _get_imap_connection(self, account: EmailAccount):
        """获取IMAP连接的上下文管理器"""
        client = None
        try:
            context = ssl.create_default_context()
            client = IMAPClient(account.host, port=account.port, ssl_context=context)
            client.login(account.email, account.password)
            client.select_folder("INBOX")
            yield client
        except Exception as e:
            logger.error(f"❌ IMAP连接失败 [{account.email}]: {e}")
            raise
        finally:
            if client:
                # IMAP登出可能因为网络问题、连接已断开等各种原因失败
                # 这里忽略所有异常，因为连接清理是尽力而为的操作
                with contextlib.suppress(
                    OSError, ConnectionError, TimeoutError, Exception
                ):
                    client.logout()

    def _fetch_new_messages(self, account: EmailAccount) -> list[EmailMessage]:
        """获取新邮件"""
        messages = []
        last_seen = self.uid_cache.get(account.email, 0)

        try:
            with self._get_imap_connection(account) as client:
                # 搜索新邮件
                uids = client.search(["ALL"])
                if not uids:
                    return messages

                # 过滤新邮件
                new_uids = [uid for uid in uids if uid > last_seen]
                if not new_uids:
                    return messages

                logger.debug(f"发现 {len(new_uids)} 封新邮件: {account.email}")

                # 获取邮件内容
                for uid in new_uids:
                    try:
                        msg_data = client.fetch([uid], ["RFC822", "INTERNALDATE"])
                        raw = email.message_from_bytes(msg_data[uid][b"RFC822"])
                        received_time = msg_data[uid].get(
                            b"INTERNALDATE", datetime.now()
                        )

                        # 解析邮件内容
                        payload = raw.get_payload(decode=True)
                        body = (
                            payload.decode("utf-8", errors="ignore") if payload else ""
                        )
                        sender = parseaddr(raw.get("From", ""))[1]
                        subject = raw.get("Subject", "")

                        # 白名单检查
                        if sender.lower() not in [
                            s.lower() for s in account.allowed_senders
                        ]:
                            logger.debug(f"⚠️ 发件人 {sender} 不在白名单中，跳过")
                            continue

                        message = EmailMessage(
                            uid=uid,
                            sender=sender,
                            subject=subject,
                            body=body,
                            received_time=received_time,
                            raw=raw,
                        )

                        messages.append(message)

                        # 标记为已读
                        client.add_flags(uid, ["\\Seen"])

                    except Exception as e:
                        logger.warning(f"⚠️ 处理邮件 UID {uid} 失败: {e}")
                        continue

                # 更新缓存
                if new_uids:
                    max_uid = max(new_uids)
                    self.uid_cache[account.email] = max_uid
                    self._save_uid_cache()

        except Exception as e:
            logger.error(f"❌ 获取新邮件失败 [{account.email}]: {e}")

        return messages

    def start_service(self):
        """启动邮件轮询服务"""
        with self._lock:
            if self._running:
                logger.warning("⚠️ 邮件轮询服务已在运行")
                return False

            try:
                # 初始化线程池
                self.thread_pool = ThreadPoolExecutor(
                    max_workers=self.config.MAX_WORKERS,
                    thread_name_prefix="email_worker",
                )

                # 初始化调度器
                self.scheduler = BackgroundScheduler(
                    timezone=self.config.TIMEZONE,
                    job_defaults={
                        "coalesce": True,
                        "max_instances": 1,
                        "misfire_grace_time": 60,
                    },
                )

                # 为每个账户添加轮询任务
                for _email_addr, account in self.accounts.items():
                    self._schedule_account_polling(account)

                # 启动调度器
                self.scheduler.start()
                self._running = True

                logger.info(
                    f"✅ 邮件轮询服务启动成功，监控 {len(self.accounts)} 个邮箱"
                )
                return True

            except Exception as e:
                logger.error(f"❌ 启动邮件轮询服务失败: {e}", exc_info=True)
                return False

    def stop_service(self):
        """停止邮件轮询服务"""
        with self._lock:
            if not self._running:
                return

            try:
                if self.scheduler and self.scheduler.running:
                    self.scheduler.shutdown(wait=False)

                if self.thread_pool:
                    self.thread_pool.shutdown(wait=False)

                self._running = False
                logger.info("✅ 邮件轮询服务已停止")

            except Exception as e:
                logger.error(f"❌ 停止邮件轮询服务失败: {e}")

    def _schedule_account_polling(self, account: EmailAccount):
        """为账户安排轮询任务"""
        # 随机选择每小时的分钟数，避免所有任务同时执行
        minute = random.randint(0, 59)
        trigger = CronTrigger(minute=minute)
        job_id = f"poll_email_{account.email.replace('@', '_').replace('.', '_')}"

        # 清除可能存在的旧任务
        if self.scheduler.get_job(job_id):
            self.scheduler.remove_job(job_id)

        # 添加新任务
        self.scheduler.add_job(
            self._safe_poll_account,
            trigger=trigger,
            args=[account],
            id=job_id,
            replace_existing=True,
            misfire_grace_time=60,
            coalesce=True,
            max_instances=1,
        )

        logger.info(f"✅ 添加轮询任务: {account.email} → 每小时第 {minute} 分钟")

    def _safe_poll_account(self, account: EmailAccount):
        """安全轮询账户（带时间窗口检查）"""
        if not self.is_within_active_window():
            logger.debug(f"⏰ 当前非活动时间，跳过轮询: {account.email}")
            return

        # 提交到线程池执行
        if self.thread_pool:
            future = self.thread_pool.submit(self._poll_account, account)
            return future

    def _poll_account(self, account: EmailAccount):
        """轮询单个账户"""
        logger.info(f"🔍 开始轮询邮箱: {account.email}")

        try:
            messages = self._fetch_new_messages(account)

            if messages:
                logger.info(f"📧 发现 {len(messages)} 封新邮件: {account.email}")

                # 处理每封邮件
                for message in messages:
                    self._process_message(message)

            else:
                logger.debug(f"📭 无新邮件: {account.email}")

        except Exception as e:
            logger.error(f"❌ 轮询邮箱失败 [{account.email}]: {e}", exc_info=True)

    def _process_message(self, message: EmailMessage):
        """处理单封邮件"""
        try:
            logger.info(f"📨 处理邮件: {message.sender} - {message.subject[:50]}...")

            # 解析邮件
            parsed_data = self.parser.parse_email(
                sender=message.sender,
                subject=message.subject,
                body=message.body,
                raw_email=message.raw,
            )

            if not parsed_data.success:
                logger.warning(f"⚠️ 邮件解析失败: {parsed_data.error}")
                return

            # 准备邮件元数据
            email_metadata = {
                "sender": message.sender,
                "subject": message.subject,
                "received_time": message.received_time.isoformat(),
                "uid": message.uid,
            }

            # 处理邮件
            result = self.processor.process_parsed_email(parsed_data, email_metadata)

            if result.success:
                logger.info(f"✅ 邮件处理成功: {result.message}")
            else:
                logger.warning(f"⚠️ 邮件处理失败: {result.message}")

        except Exception as e:
            logger.error(f"❌ 处理邮件失败: {e}", exc_info=True)

    def health_check(self) -> dict:
        """健康检查"""
        return {
            "service": "unified_email_service",
            "status": "healthy" if self._running else "stopped",
            "timestamp": datetime.now().isoformat(),
            "accounts_count": len(self.accounts),
            "scheduler_running": self.scheduler.running if self.scheduler else False,
            "jobs_count": len(self.scheduler.get_jobs())
            if self.scheduler and self.scheduler.running
            else 0,
        }
