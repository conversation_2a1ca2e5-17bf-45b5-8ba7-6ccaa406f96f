"""添加order_id到visa_status_history表以完善订单级别状态追踪

Revision ID: add_order_id_to_visa_status_history
Revises: add_visa_payment_fields
Create Date: 2025-06-22 16:00:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "add_order_id_visa_status"
down_revision = "add_visa_payment_fields"
branch_labels = None
depends_on = None


def upgrade():
    """
    🔐 完善订单级别状态追踪：为visa_status_history表添加order_id字段

    目标：
    1. 支持订单级别的状态历史查询
    2. 简化多申请订单的状态统计
    3. 提升订单详情页面的查询性能
    4. 保持与其他表的架构一致性
    """
    print("\n🔐 开始完善订单级别状态追踪...")
    print("=" * 60)

    # 第一阶段：添加order_id字段到visa_status_history表
    print("\n📋 第一阶段：为visa_status_history表添加order_id字段")
    print("-" * 50)

    print("  🔧 添加order_id字段...")
    op.add_column(
        "visa_status_history",
        sa.Column("order_id", postgresql.UUID(as_uuid=True), nullable=True),
    )

    print("  📊 填充visa_status_history.order_id数据...")
    # 通过application表填充order_id
    op.execute("""
        UPDATE visa_status_history
        SET order_id = a.order_id
        FROM application a
        WHERE visa_status_history.application_id = a.id
    """)

    print("  🔒 设置visa_status_history.order_id为NOT NULL...")
    op.alter_column("visa_status_history", "order_id", nullable=False)

    print("  🔗 添加visa_status_history.order_id外键约束...")
    op.create_foreign_key(
        "visa_status_history_order_id_fkey",
        "visa_status_history",
        "order",
        ["order_id"],
        ["id"],
        ondelete="CASCADE",
    )

    print("  📊 创建visa_status_history.order_id索引...")
    op.create_index(
        "ix_visa_status_history_order_id", "visa_status_history", ["order_id"]
    )

    # 第二阶段：创建复合索引优化查询性能
    print("\n📋 第二阶段：创建订单级别查询优化索引")
    print("-" * 50)

    print("  🎯 创建visa_status_history复合索引...")
    op.create_index(
        "ix_visa_status_history_order_status",
        "visa_status_history",
        ["order_id", "visa_status"],
    )
    op.create_index(
        "ix_visa_status_history_order_created",
        "visa_status_history",
        ["order_id", "created_at"],
    )

    # 验证修复结果
    print("\n🔍 验证订单级别状态追踪修复结果...")
    print("-" * 50)

    # 检查数据完整性
    connection = op.get_bind()

    # 验证visa_status_history表order_id
    order_id_count = connection.execute(
        sa.text("SELECT COUNT(*) FROM visa_status_history WHERE order_id IS NULL")
    ).scalar()

    if order_id_count == 0:
        print("  ✅ visa_status_history表order_id数据完整")
    else:
        print(f"  ❌ visa_status_history表有{order_id_count}条记录缺少order_id")

    # 验证数据一致性
    consistency_check = connection.execute(
        sa.text("""
            SELECT COUNT(*) FROM visa_status_history v
            JOIN application a ON v.application_id = a.id
            WHERE v.order_id != a.order_id
        """)
    ).scalar()

    if consistency_check == 0:
        print("  ✅ visa_status_history表数据一致性验证通过")
    else:
        print(f"  ❌ 发现{consistency_check}条数据不一致记录")

    # 验证订单级别查询能力
    sample_check = connection.execute(
        sa.text("""
            SELECT COUNT(DISTINCT order_id) as order_count,
                   COUNT(*) as history_count
            FROM visa_status_history
        """)
    ).fetchone()

    if sample_check:
        print(
            f"  📊 状态历史统计：{sample_check.order_count}个订单，{sample_check.history_count}条历史记录"
        )

    print("\n🎉 订单级别状态追踪完善完成！")
    print("📋 修复摘要：")
    print("  ✅ visa_status_history表添加order_id字段和索引")
    print("  ✅ 创建外键约束确保数据一致性")
    print("  ✅ 创建复合索引优化订单级别查询")
    print("  🔐 现在支持高效的订单级别状态追踪")
    print("\n💡 订单级别状态追踪优势：")
    print("  📊 订单详情页面：快速查询所有申请状态")
    print("  📈 订单进度统计：高效聚合状态数据")
    print("  👨‍👩‍👧‍👦 家庭签证支持：统一管理多申请状态")
    print("  🔗 架构一致性：与automation_logs、visa_payment保持一致")


def downgrade():
    """
    回滚订单级别状态追踪修复
    """
    print("\n🔄 回滚订单级别状态追踪修复...")

    # 删除复合索引
    op.drop_index("ix_visa_status_history_order_created", "visa_status_history")
    op.drop_index("ix_visa_status_history_order_status", "visa_status_history")

    # 删除单字段索引
    op.drop_index("ix_visa_status_history_order_id", "visa_status_history")

    # 删除外键约束
    op.drop_constraint(
        "visa_status_history_order_id_fkey", "visa_status_history", type_="foreignkey"
    )

    # 删除字段
    op.drop_column("visa_status_history", "order_id")

    print("  ✅ 订单级别状态追踪修复已回滚")
