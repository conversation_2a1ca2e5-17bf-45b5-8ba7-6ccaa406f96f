{"name": "visa-automator", "version": "1.0.0", "description": "签证自动化系统 - Vietnam Visa Automation System", "private": true, "type": "module", "workspaces": ["frontend"], "scripts": {"dev": "npm run dev --workspace=frontend", "build": "npm run build --workspace=frontend", "lint": "npm run lint --workspace=frontend", "lint:style": "npm run lint:style --workspace=frontend", "format": "npm run format --workspace=frontend", "type-check": "npm run type-check --workspace=frontend", "test": "npm run test:unit --workspace=frontend", "install:all": "npm install && npm install --workspace=frontend", "clean": "rimraf node_modules frontend/node_modules frontend/dist frontend/.vite", "pre-commit": "pre-commit run --all-files"}, "devDependencies": {"lint-staged": "^15.2.10", "rimraf": "^6.0.1"}, "lint-staged": {"frontend/**/*.{js,ts,vue}": ["bash -c 'cd frontend && npx eslint --fix --no-error-on-unmatched-pattern'", "bash -c 'cd frontend && npx prettier --write --ignore-unknown'"], "frontend/**/*.{css,scss,vue}": ["bash -c 'cd frontend && npx stylelint --fix --allow-empty-input'"]}, "repository": {"type": "git", "url": "https://github.com/vivi196/visa-automator.git"}, "keywords": ["visa", "automation", "vietnam", "<PERSON><PERSON><PERSON>", "vue"], "author": "Visa Automator Team", "license": "MIT", "engines": {"node": ">=20.16.0", "npm": ">=10.0.0"}}