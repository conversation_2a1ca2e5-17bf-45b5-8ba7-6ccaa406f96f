# backend/routes/visa/status.py
"""
签证状态查询功能模块

遵循PP和QQ Notepad要求：
- 单一职责：专注于状态查询逻辑
- API兼容性：保持现有接口完全兼容
- 异常处理：全链路日志与异常兜底
- 详细状态步骤显示
"""

from fastapi import APIRouter, Depends, HTTPException

from app.utils.logger_config import get_logger
from backend.auth_fastapi_users.dependencies import require_auth
from backend.db_config.unified_connection import get_unified_db

logger = get_logger()

router = APIRouter()


@router.get("/status/{application_id}")
async def get_visa_status(application_id: str, user=Depends(require_auth)):
    """
    查询签证申请状态

    - **application_id**: 申请编号或护照号码
    - **返回**: 申请状态信息
    """
    try:
        logger.info(f"🔍 开始查询申请状态: {application_id}")

        db = await get_unified_db()

        # 查询申请信息（支持申请编号或护照号码）
        applications = await db.query_applications_by_identifier(application_id)

        if not applications:
            logger.warning(f"⚠️ 未找到申请记录: {application_id}")
            raise HTTPException(status_code=404, detail="未找到相关申请记录")

        # 获取最新的申请记录
        latest_app = applications[0]
        logger.info(
            f"✅ 找到申请记录，申请编号: {latest_app.get('application_number')}"
        )

        # 构建状态步骤
        steps = []

        # 提交步骤
        if latest_app.get("created_at"):
            steps.append(
                {
                    "step": "submitted",
                    "status": "completed",
                    "time": latest_app["created_at"].isoformat()
                    if latest_app["created_at"]
                    else None,
                    "description": "申请已提交",
                }
            )

        # 处理步骤
        if latest_app.get("processing_started_at"):
            steps.append(
                {
                    "step": "processing",
                    "status": "completed"
                    if latest_app.get("processing_completed_at")
                    else "in_progress",
                    "time": latest_app["processing_started_at"].isoformat()
                    if latest_app["processing_started_at"]
                    else None,
                    "description": "正在处理申请",
                }
            )
        elif latest_app.get("submit_status") == "SUCCESS":
            steps.append(
                {
                    "step": "processing",
                    "status": "in_progress",
                    "time": None,
                    "description": "正在处理申请",
                }
            )

        # 支付步骤
        payment_status = (
            "completed" if latest_app.get("submit_status") == "SUCCESS" else "pending"
        )
        steps.append(
            {
                "step": "payment",
                "status": payment_status,
                "time": latest_app["created_at"].isoformat()
                if payment_status == "completed" and latest_app.get("created_at")
                else None,
                "description": "支付处理",
            }
        )

        # 审批步骤
        approval_status = latest_app.get("approval_status", "WAITING")
        approval_step_status = (
            "completed"
            if approval_status == "APPROVED"
            else ("failed" if approval_status == "REJECTED" else "pending")
        )
        steps.append(
            {
                "step": "approval",
                "status": approval_step_status,
                "time": latest_app.get("processing_completed_at").isoformat()
                if latest_app.get("processing_completed_at")
                and approval_status != "WAITING"
                else None,
                "description": "签证审批",
            }
        )

        # 构建申请人姓名
        applicant_name = (
            latest_app.get("chinese_name")
            or f"{latest_app.get('surname', '')} {latest_app.get('given_name', '')}".strip()
        )

        # 🔥 返回与原始visa.py完全兼容的字典格式
        response = {
            "success": True,
            "message": "状态查询成功",
            "application_id": latest_app.get("application_number", application_id),
            "applicant_name": applicant_name,
            "passport_number": latest_app.get("passport_number"),
            "status": latest_app.get("submit_status", "PENDING"),
            "approval_status": latest_app.get("approval_status", "WAITING"),
            "last_updated": latest_app.get(
                "updated_at", latest_app.get("created_at")
            ).isoformat()
            if latest_app.get("updated_at") or latest_app.get("created_at")
            else None,
            "visa_type": latest_app.get("visa_type"),
            "visa_validity_days": latest_app.get("visa_validity_days"),
            "steps": steps,
        }

        logger.info(
            f"✅ 状态查询成功: {application_id} - {latest_app.get('submit_status')}"
        )
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 状态查询失败: {application_id} - {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"状态查询失败: {str(e)}")


@router.get("/user/history/{passport_number}")
async def get_user_history(passport_number: str, user=Depends(require_auth)):
    """
    获取用户申请历史记录

    - **passport_number**: 护照号码
    - **返回**: 用户的所有申请记录
    """
    try:
        logger.info(f"🔍 开始查询用户历史记录: {passport_number}")

        db = await get_unified_db()
        applications = await db.get_user_applications(passport_number)

        if not applications:
            logger.info(f"📝 暂无申请记录: {passport_number}")
            # 🔥 返回与原始visa.py完全兼容的字典格式
            return {
                "success": True,
                "message": "暂无申请记录",
                "passport_number": passport_number,
                "applications": [],
            }

        # 格式化申请记录
        formatted_applications = []
        for app in applications:
            # 构建申请人姓名
            applicant_name = (
                app.get("chinese_name")
                or f"{app.get('surname', '')} {app.get('given_name', '')}".strip()
            )

            formatted_app = {
                "application_id": app.get("application_number"),
                "applicant_name": applicant_name,
                "visa_type": app.get("visa_type"),
                "visa_validity_days": app.get("visa_validity_days"),
                "visa_start_date": app.get("visa_start_date"),
                "submit_status": app.get("submit_status"),
                "approval_status": app.get("approval_status"),
                "submission_time": app.get("created_at").isoformat()
                if app.get("created_at")
                else None,
                "last_updated": app.get("updated_at", app.get("created_at")).isoformat()
                if app.get("updated_at") or app.get("created_at")
                else None,
            }
            formatted_applications.append(formatted_app)

        # 🔥 返回与原始visa.py完全兼容的字典格式
        response = {
            "success": True,
            "message": f"找到 {len(applications)} 条申请记录",
            "passport_number": passport_number,
            "applications": formatted_applications,
        }

        logger.info(
            f"✅ 历史记录查询成功: {passport_number} - 找到{len(applications)}条记录"
        )
        return response

    except Exception as e:
        logger.error(
            f"❌ 获取用户历史记录失败: {passport_number} - {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")


@router.get("/applications")
async def list_applications(
    page: int = 1, page_size: int = 10, status: str = None, user=Depends(require_auth)
):
    """
    获取申请列表

    - **page**: 页码
    - **page_size**: 每页大小
    - **status**: 状态筛选
    - **返回**: 申请列表
    """
    try:
        logger.info(
            f"🔍 开始查询申请列表: page={page}, page_size={page_size}, status={status}"
        )

        # 参数验证
        if page_size > 100:
            page_size = 100
        if page < 1:
            page = 1

        # 这里应该从数据库查询申请列表
        # 暂时返回模拟数据，保持API兼容性
        applications = [
            {
                "application_id": f"E2025012300{i}",
                "applicant_name": f"申请人{i}",
                "status": "processing" if i % 2 == 0 else "completed",
                "submission_time": "2025-01-01T10:00:00",
                "last_updated": "2025-01-01T12:00:00",
            }
            for i in range(1, 6)
        ]

        # 状态筛选
        if status:
            applications = [app for app in applications if app["status"] == status]

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        paginated_apps = applications[start:end]

        # 🔥 返回与原始visa.py完全兼容的字典格式
        response = {
            "success": True,
            "message": "申请列表获取成功",
            "data": paginated_apps,
            "total": len(applications),
            "page": page,
            "page_size": page_size,
        }

        logger.info(f"✅ 申请列表查询成功: 返回{len(paginated_apps)}条记录")
        return response

    except Exception as e:
        logger.error(f"❌ 获取申请列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取申请列表失败: {str(e)}")


# 注意：cancel_application功能已在application.py中实现
# 路径为 POST /api/visa/cancel，避免重复定义


# 🔥 新架构：任务状态查询接口
@router.get("/task-status/{task_id}")
async def get_task_status(task_id: str, user=Depends(require_auth)):
    """
    查询Celery任务状态

    新架构：直接从automation_logs查询，实时准确
    """
    try:
        logger.info(f"🔍 查询任务状态: {task_id}")

        db = await get_unified_db()

        async with db.get_connection() as conn:
            from sqlalchemy import select

            from app.data.models.automation_logs import AutomationLogs

            # 查询automation_logs记录
            stmt = select(AutomationLogs).where(
                AutomationLogs.celery_task_id == task_id
            )
            result = await conn.execute(stmt)
            log_entry = result.fetchone()

            if not log_entry:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 返回任务状态
            return {
                "success": True,
                "data": {
                    "task_id": task_id,
                    "status": log_entry.task_status,
                    "started_at": log_entry.started_at.isoformat()
                    if log_entry.started_at
                    else None,
                    "completed_at": log_entry.completed_at.isoformat()
                    if log_entry.completed_at
                    else None,
                    "result": log_entry.result_data,
                    "error": log_entry.error_message,
                    "updated_at": log_entry.updated_at.isoformat(),
                },
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 查询任务状态失败: {task_id} - {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")
