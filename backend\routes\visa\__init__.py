# backend/routes/visa/__init__.py
"""
签证申请路由模块入口

遵循PP和QQ Notepad要求：
- 路由注册和依赖注入：组装所有子路由
- API兼容性：保持现有接口完全兼容
- 依赖注入模式：统一管理依赖
- 异常处理：全局异常捕获
"""

from fastapi import APIRouter, HTTPException

# 导入共享依赖和工具
# from app.services.order_service import OrderService  # 未使用，已注释
# from backend.db_config.unified_connection import get_unified_db  # 未使用，已注释
# from backend.auth_fastapi_users.dependencies import require_auth, require_admin_auth  # 未使用，已注释
from app.utils.logger_config import get_logger
from backend.routes.visa.admin import router as admin_router

# from fastapi import Depends  # 未使用，已注释
# from typing import Dict, Any  # 未使用，已注释
# 导入所有子路由
from backend.routes.visa.application import router as application_router
from backend.routes.visa.export import router as export_router
from backend.routes.visa.file_handling import router as file_handling_router
from backend.routes.visa.status import router as status_router

logger = get_logger()

# 创建主路由器，保持原有的prefix和tags
router = APIRouter(prefix="/visa", tags=["Visa Application"])

# 全局订单服务实例 - 统一管理
order_service = None


async def get_order_service():
    """
    获取订单服务实例 - 统一依赖注入

    Returns:
        OrderService: 订单服务实例
    """
    try:
        # 修复：使用新的依赖注入模式
        from app.repositories.order_repository import OrderRepository
        from app.services.order_service import OrderService
        from backend.auth_fastapi_users.database import get_async_session

        # 正确使用async context manager
        async with get_async_session() as session:
            # 创建Repository和Service
            order_repository = OrderRepository(session)
            service = OrderService(order_repository)
            await service.initialize()
            logger.info("✅ 订单服务初始化成功")
            return service
    except Exception as e:
        logger.error(f"❌ 订单服务获取失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务初始化失败")


# ===== 包含所有子路由 =====

# 1. 申请提交相关路由 (application.py)
router.include_router(
    application_router,
    dependencies=[],  # 认证依赖在各个路由中单独处理
    responses={
        422: {"description": "表单验证错误"},
        500: {"description": "服务器内部错误"},
    },
)

# 2. 状态查询相关路由 (status.py)
router.include_router(
    status_router,
    dependencies=[],  # 认证依赖在各个路由中单独处理
    responses={
        404: {"description": "申请记录未找到"},
        500: {"description": "服务器内部错误"},
    },
)

# 3. 文件处理和工具路由 (file_handling.py)
router.include_router(
    file_handling_router,
    dependencies=[],  # 部分路由需要认证，部分不需要
    responses={
        400: {"description": "参数错误"},
        500: {"description": "服务器内部错误"},
    },
)

# 4. 管理员功能路由 (admin.py)
router.include_router(
    admin_router,
    dependencies=[],  # 认证依赖在各个路由中单独处理
    responses={
        403: {"description": "权限不足"},
        500: {"description": "服务器内部错误"},
    },
)

# 5. 数据导出路由 (export.py)
router.include_router(
    export_router,
    dependencies=[],  # 认证依赖在各个路由中单独处理
    responses={400: {"description": "导出参数错误"}, 500: {"description": "导出失败"}},
)

# ===== 健康检查和状态路由 =====


@router.get("/health")
async def health_check():
    """
    健康检查接口

    Returns:
        Dict: 健康状态信息
    """
    try:
        # 检查数据库连接
        from backend.db_config.unified_connection import get_unified_db

        # 获取统一数据库连接（如果已初始化）
        unified_db = await get_unified_db()

        # 执行简单的连接测试
        if unified_db and unified_db.engine:
            # 简单的连接测试，不触发建表
            async with unified_db.get_connection() as conn:
                from sqlalchemy import text

                await conn.execute(text("SELECT 1"))
            db_status = "healthy"
        else:
            db_status = "not_initialized"

        return {
            "status": "healthy",
            "timestamp": "2025-06-16T11:48:00Z",
            "services": {
                "database": db_status,
                "database_type": "postgresql",
                "routes": "loaded",
            },
        }
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": "2025-06-16T11:48:00Z",
            "services": {"database": "error", "error": str(e), "routes": "loaded"},
        }


@router.get("/routes")
async def list_routes():
    """
    列出所有可用路由

    Returns:
        Dict: 路由列表
    """
    try:
        routes_info = {
            "申请相关": {
                "POST /visa/apply": "提交签证申请",
                "GET /visa/calculate-expedited-date": "计算加急日期",
                "GET /visa/check-duplicate/{passport_number}": "检查重复提交",
            },
            "状态查询": {
                "GET /visa/status/{application_id}": "查询申请状态",
                "GET /visa/user/history/{passport_number}": "获取用户历史记录",
                "GET /visa/applications": "获取申请列表",
                "DELETE /visa/applications/{application_id}": "取消申请",
            },
            "管理员功能": {
                "GET /visa/admin/dashboard": "管理员仪表板",
                "GET /visa/admin/query": "管理员查询申请",
                "GET /visa/admin/statistics": "统计数据",
                "GET /visa/admin/application/{application_number}": "获取申请详情",
                "GET /visa/admin/export": "导出数据",
            },
            "系统": {"GET /visa/health": "健康检查", "GET /visa/routes": "路由列表"},
        }

        return {
            "success": True,
            "message": "路由列表获取成功",
            "routes": routes_info,
            "total_routes": sum(len(routes) for routes in routes_info.values()),
        }
    except Exception as e:
        logger.error(f"❌ 获取路由列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取路由列表失败: {str(e)}")


# ===== 全局异常处理说明 =====

# 注意：APIRouter不支持exception_handler装饰器
# 全局异常处理应该在FastAPI应用级别设置
# 这里我们将异常处理逻辑集成到各个路由函数中

# 如果需要全局异常处理，应该在main.py中添加：
# @app.exception_handler(Exception)
# async def global_exception_handler(request, exc):
#     logger.error(f"❌ 全局异常捕获: {str(exc)}", exc_info=True)
#     return {
#         "success": False,
#         "message": "服务器内部错误",
#         "error": str(exc),
#         "timestamp": datetime.utcnow().isoformat()
#     }

# ===== 模块导出 =====

# 确保向后兼容性，导出所有需要的组件
__all__ = [
    "router",
    "get_order_service",
    # 子模块也可以直接导入使用
    "application_router",
    "status_router",
    "file_handling_router",
    "admin_router",
    "export_router",
]

# 模块初始化日志
logger.info("✅ 签证申请路由模块加载完成")
logger.info("📋 已注册路由: application, status, file_handling, admin, export")
logger.info("🔧 依赖注入: order_service, auth_dependencies")
logger.info("✅ 异常处理: 全局异常捕获已启用")
