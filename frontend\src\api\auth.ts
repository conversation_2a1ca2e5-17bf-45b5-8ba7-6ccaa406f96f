import type { ApiResponse, LoginRequest, LoginResponse, UserInfoResponse } from '@/types/auth'
import axios from 'axios'

// 创建axios实例
const authApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL ?? '', // 使用空字符串，通过Vite代理
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 自动添加token
authApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器 - 处理token失效
authApi.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token失效，清除本地存储并跳转到登录页
      localStorage.removeItem('auth_token')
      console.log('🔄 Token失效，重定向到登录页')
      // 使用Vue Router的正确路径格式
      window.location.href = '/login'
    }
    return Promise.reject(error)
  },
)

// 登录API - 适配fastapi-users后端
export const loginApi = async (loginData: LoginRequest): Promise<LoginResponse> => {
  try {
    // 使用URLSearchParams格式发送数据，匹配fastapi-users的Form接收
    const formData = new URLSearchParams()
    formData.append('username', loginData.username)
    formData.append('password', loginData.password)

    console.log('🚀 发送登录请求:', { username: loginData.username })

    const response = await authApi.post('/api/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })

    console.log('📥 后端响应:', response.data)

    // 后端返回JSON响应，包含token和用户信息
    if (response.status === 200 && response.data) {
      const responseData = response.data

      // 检查是否是fastapi-users的标准响应格式
      if (responseData.access_token && responseData.user) {
        const { access_token, user } = responseData

        return {
          success: true,
          message: '登录成功',
          data: {
            token: access_token,
            user: {
              id: user.id,
              username: user.email ? user.email.split('@')[0] : (user.username ?? 'user'),
              email: user.email,
              role: user.is_superuser ? 'admin' : 'user',
              createdAt: user.created_at ?? new Date().toISOString(),
              updatedAt: user.updated_at ?? new Date().toISOString(),
            },
            expiresIn: 3600, // 1小时
          },
          code: 200,
        }
      }

      // 检查是否是自定义的成功响应格式
      if (responseData.success && responseData.data) {
        return responseData
      }

      // 如果响应格式不符合预期，但状态码是200，尝试解析
      console.log('⚠️ 未知的响应格式:', responseData)
    }

    return {
      success: false,
      message: '登录失败：服务器响应格式错误',
      data: {
        token: '',
        user: {
          id: '',
          username: '',
          email: '',
          role: '',
          createdAt: '',
          updatedAt: '',
        },
        expiresIn: 0,
      },
      code: response.status,
    }
  } catch (error: unknown) {
    console.error('💥 登录API错误:', error)

    let errorMessage = '登录失败，请检查网络连接'

    // 类型安全的错误处理
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as {
        response?: { status?: number; data?: { detail?: string; message?: string } }
      }

      if (axiosError.response?.status === 400) {
        errorMessage = '用户名或密码错误'
      } else if (axiosError.response?.status === 422) {
        errorMessage = '请求格式错误，请检查输入'
      } else if (axiosError.response?.data?.detail) {
        errorMessage = axiosError.response.data.detail
      } else if (axiosError.response?.data?.message) {
        errorMessage = axiosError.response.data.message
      }

      return {
        success: false,
        message: errorMessage,
        data: {
          token: '',
          user: {
            id: '',
            username: '',
            email: '',
            role: '',
            createdAt: '',
            updatedAt: '',
          },
          expiresIn: 0,
        },
        code: axiosError.response?.status ?? 500,
      }
    }

    return {
      success: false,
      message: errorMessage,
      data: {
        token: '',
        user: {
          id: '',
          username: '',
          email: '',
          role: '',
          createdAt: '',
          updatedAt: '',
        },
        expiresIn: 0,
      },
      code: 500,
    }
  }
}

// 获取用户信息API - 使用正确的端点
export const getUserInfoApi = async (): Promise<UserInfoResponse> => {
  try {
    const response = await authApi.get('/api/session-status')

    if (response.status === 200 && response.data.success) {
      const userData = response.data
      return {
        success: true,
        message: '获取用户信息成功',
        data: {
          id: userData.id ?? 'user-id',
          username: userData.username ?? userData.email?.split('@')[0] ?? 'user',
          email: userData.email,
          role: userData.admin ? 'admin' : 'user',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      }
    }

    return {
      success: false,
      message: '用户未认证',
      data: {
        id: '',
        username: '',
        email: '',
        role: '',
        createdAt: '',
        updatedAt: '',
      },
      code: 401,
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : '获取用户信息失败'
    const statusCode =
      error && typeof error === 'object' && 'response' in error
        ? ((error as { response?: { status?: number } }).response?.status ?? 500)
        : 500

    return {
      success: false,
      message: errorMessage,
      data: {
        id: '',
        username: '',
        email: '',
        role: '',
        createdAt: '',
        updatedAt: '',
      },
      code: statusCode,
    }
  }
}

// 登出API
export const logoutApi = async (): Promise<ApiResponse> => {
  try {
    // fastapi-users没有标准的logout端点，我们只需要清除本地token
    return {
      success: true,
      message: '已退出登录',
      data: null,
    }
  } catch {
    // 即使登出API失败，也应该清除本地状态
    return {
      success: true,
      message: '已退出登录',
      data: null,
    }
  }
}

// 刷新Token API
export const refreshTokenApi = async (): Promise<LoginResponse> => {
  try {
    const response = await authApi.post('/auth/refresh')
    return response.data
  } catch (error: unknown) {
    const statusCode =
      error && typeof error === 'object' && 'response' in error
        ? ((error as { response?: { status?: number } }).response?.status ?? 500)
        : 500

    return {
      success: false,
      message: 'Token刷新失败',
      data: {
        token: '',
        user: {
          id: '',
          username: '',
          email: '',
          role: '',
          createdAt: '',
          updatedAt: '',
        },
        expiresIn: 0,
      },
      code: statusCode,
    }
  }
}

export default authApi
