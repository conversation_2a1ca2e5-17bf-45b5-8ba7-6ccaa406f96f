"""
Logger Configuration Module

Configures and initializes the application logging system.
"""

from datetime import datetime
from pathlib import Path
import sys
from zoneinfo import ZoneInfo

from loguru import logger


def setup_logger(
    log_dir: str = "logs",
    console_level: str = "INFO",
    file_level: str = "DEBUG",
    log_filename_prefix: str = "vietnam_evisa_app",  # <--- 1. 添加这个参数，并给一个合适的默认值
) -> None:
    """
    Set up the application logger.

    Configures the logger to output to both console and log files.

    Args:
        log_dir: Directory to store log files
        console_level: Log level for console output
        file_level: Log level for file output
    """
    # Create log directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)

    # 🔧 2025年最佳实践：使用zoneinfo替代pytz
    shanghai_tz = ZoneInfo("Asia/Shanghai")

    # Generate log filename with timestamp (使用上海时间)
    timestamp = datetime.now(shanghai_tz).strftime("%Y%m%d_%H%M%S")
    log_filename = log_path / f"{log_filename_prefix}_{timestamp}.log"

    # Remove default handler
    logger.remove()

    # 🔧 关键修复：定义时区过滤器函数
    def timezone_filter(record):
        record["time"] = record["time"].astimezone(shanghai_tz)
        return True

    # Add console handler (使用上海时区)
    logger.add(
        sys.stderr,
        level=console_level.upper(),
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        filter=timezone_filter,
    )

    # Add file handler (使用上海时区)
    logger.add(
        str(log_filename),
        level=file_level.upper(),
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="1 week",
        compression="zip",
        filter=timezone_filter,
    )

    logger.info(f"Logger initialized. Log file: {log_filename}")


def get_logger():
    """
    Get the configured logger.

    Returns:
        Configured logger instance
    """
    return logger
